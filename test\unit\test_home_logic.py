"""
Unit tests for HomeLogic class.
"""
import pytest
from unittest.mock import AsyncMock, MagicMock, patch

from app.modules.home.home_logic import HomeLogic
from app.modules.home.home_service import HomeService
from app.general_logic_helper.controllers.data_controller import DataController
from app.general_logic_helper.handlers.measurements import Measurements


class TestHomeLogic:
    """Test cases for HomeLogic class."""
    
    @pytest.fixture
    def mock_home_service(self):
        """Mock HomeService for testing."""
        return AsyncMock(spec=HomeService)
    
    @pytest.fixture
    def mock_data_controller(self):
        """Mock DataController for testing."""
        return AsyncMock(spec=DataController)
    
    @pytest.fixture
    def mock_measurements(self):
        """Mock Measurements for testing."""
        return AsyncMock(spec=Measurements)
    
    @pytest.fixture
    def home_logic(self, mock_home_service, mock_data_controller, mock_measurements):
        """HomeLogic instance with mocked dependencies."""
        return HomeLogic(mock_home_service, mock_data_controller, mock_measurements)
    
    def test_home_logic_init(self, mock_home_service, mock_data_controller, mock_measurements):
        """Test HomeLogic initialization."""
        logic = HomeLogic(mock_home_service, mock_data_controller, mock_measurements)
        
        assert logic.home_service == mock_home_service
        assert logic.data_controller == mock_data_controller
        assert logic.measurements == mock_measurements
    
    @pytest.mark.asyncio
    async def test_owners_with_vessel_success(self, home_logic, mock_home_service):
        """Test owners_with_vessel method with successful response."""
        # Setup
        owner_vat = "NO950700939"
        expected_result = {"owners": [{"vat": owner_vat, "vessels": []}]}
        mock_home_service.owners_with_vessel.return_value = expected_result
        
        # Execute
        result = await home_logic.owners_with_vessel(owner_vat)
        
        # Assert
        assert result == expected_result
        mock_home_service.owners_with_vessel.assert_called_once_with(owner_vat=owner_vat)
    
    @pytest.mark.asyncio
    async def test_owners_with_vessel_empty_result(self, home_logic, mock_home_service):
        """Test owners_with_vessel method with empty result."""
        # Setup
        owner_vat = "NO950700939"
        mock_home_service.owners_with_vessel.return_value = {"owners": []}
        
        # Execute
        result = await home_logic.owners_with_vessel(owner_vat)
        
        # Assert
        assert result == {"owners": []}
        mock_home_service.owners_with_vessel.assert_called_once_with(owner_vat=owner_vat)
    
    @pytest.mark.asyncio
    async def test_home_success(self, home_logic, mock_home_service, mock_measurements, mock_data_controller):
        """Test home method with successful response."""
        # Setup
        params = {
            "from_date": "2024-01-01 00:00:00",
            "owner_vat": "NO950700939",
            "to_date": "2024-01-07 23:59:59",
            "vessel_id": 32,
            "vessel_imo": "8918461",
            "vessel_name": "Wilson Saga"
        }
        
        service_data = {"measurements": [], "vessel_info": {}}
        final_result = {"processed_data": "test"}
        
        mock_home_service.home.return_value = service_data
        mock_measurements.get_and_set_measurements.return_value = None
        mock_data_controller.wss_serve.return_value = final_result
        
        # Execute
        with patch('app.modules.home.home_logic.rename_vat') as mock_rename_vat:
            mock_rename_vat.return_value = {"selected_owner_vat": "NO950700939", "vessel_imo": "8918461"}
            
            result = await home_logic.home(**params)
        
        # Assert
        assert result == final_result
        mock_rename_vat.assert_called_once()
        mock_home_service.home.assert_called_once()
        mock_measurements.get_and_set_measurements.assert_called_once()
        mock_data_controller.wss_serve.assert_called_once_with(service_data, {"selected_owner_vat": "NO950700939", "vessel_imo": "8918461"})
    
    @pytest.mark.asyncio
    async def test_home_none_data(self, home_logic, mock_home_service, mock_measurements, mock_data_controller):
        """Test home method when service returns None."""
        # Setup
        params = {
            "from_date": "2024-01-01 00:00:00",
            "owner_vat": "NO950700939",
            "to_date": "2024-01-07 23:59:59",
            "vessel_id": 32,
            "vessel_imo": "8918461",
            "vessel_name": "Wilson Saga"
        }
        
        mock_home_service.home.return_value = None
        
        # Execute
        with patch('app.modules.home.home_logic.rename_vat') as mock_rename_vat:
            mock_rename_vat.return_value = {"selected_owner_vat": "NO950700939"}
            
            result = await home_logic.home(**params)
        
        # Assert
        assert result is None
        mock_home_service.home.assert_called_once()
        mock_measurements.get_and_set_measurements.assert_not_called()
        mock_data_controller.wss_serve.assert_not_called()
    
    @pytest.mark.asyncio
    async def test_home_service_exception(self, home_logic, mock_home_service):
        """Test home method when service raises exception."""
        # Setup
        params = {
            "from_date": "2024-01-01 00:00:00",
            "owner_vat": "NO950700939",
            "to_date": "2024-01-07 23:59:59",
            "vessel_id": 32,
            "vessel_imo": "8918461",
            "vessel_name": "Wilson Saga"
        }
        
        mock_home_service.home.side_effect = Exception("Service error")
        
        # Execute & Assert
        with patch('app.modules.home.home_logic.rename_vat') as mock_rename_vat:
            mock_rename_vat.return_value = {"selected_owner_vat": "NO950700939"}
            
            with pytest.raises(Exception, match="Service error"):
                await home_logic.home(**params)
    
    @pytest.mark.asyncio
    async def test_home_measurements_exception(self, home_logic, mock_home_service, mock_measurements):
        """Test home method when measurements processing raises exception."""
        # Setup
        params = {
            "from_date": "2024-01-01 00:00:00",
            "owner_vat": "NO950700939",
            "to_date": "2024-01-07 23:59:59",
            "vessel_id": 32,
            "vessel_imo": "8918461",
            "vessel_name": "Wilson Saga"
        }
        
        service_data = {"measurements": [], "vessel_info": {}}
        mock_home_service.home.return_value = service_data
        mock_measurements.get_and_set_measurements.side_effect = Exception("Measurements error")
        
        # Execute & Assert
        with patch('app.modules.home.home_logic.rename_vat') as mock_rename_vat:
            mock_rename_vat.return_value = {"selected_owner_vat": "NO950700939"}
            
            with pytest.raises(Exception, match="Measurements error"):
                await home_logic.home(**params)
    
    @pytest.mark.asyncio
    async def test_home_data_controller_exception(self, home_logic, mock_home_service, mock_measurements, mock_data_controller):
        """Test home method when data controller raises exception."""
        # Setup
        params = {
            "from_date": "2024-01-01 00:00:00",
            "owner_vat": "NO950700939",
            "to_date": "2024-01-07 23:59:59",
            "vessel_id": 32,
            "vessel_imo": "8918461",
            "vessel_name": "Wilson Saga"
        }
        
        service_data = {"measurements": [], "vessel_info": {}}
        mock_home_service.home.return_value = service_data
        mock_measurements.get_and_set_measurements.return_value = None
        mock_data_controller.wss_serve.side_effect = Exception("Data controller error")
        
        # Execute & Assert
        with patch('app.modules.home.home_logic.rename_vat') as mock_rename_vat:
            mock_rename_vat.return_value = {"selected_owner_vat": "NO950700939"}
            
            with pytest.raises(Exception, match="Data controller error"):
                await home_logic.home(**params)
    
    @pytest.mark.asyncio
    async def test_home_parameter_validation(self, home_logic, mock_home_service, mock_measurements, mock_data_controller):
        """Test home method parameter validation through rename_vat."""
        # Setup
        params = {
            "from_date": "2024-01-01 00:00:00",
            "owner_vat": "NO950700939",
            "to_date": "2024-01-07 23:59:59",
            "vessel_id": 32,
            "vessel_imo": "8918461",
            "vessel_name": "Wilson Saga"
        }
        
        service_data = {"measurements": []}
        final_result = {"processed_data": "test"}
        
        mock_home_service.home.return_value = service_data
        mock_measurements.get_and_set_measurements.return_value = None
        mock_data_controller.wss_serve.return_value = final_result
        
        # Execute
        with patch('app.modules.home.home_logic.rename_vat') as mock_rename_vat:
            expected_payload = {
                "selected_owner_vat": "NO950700939",
                "from_date": "2024-01-01 00:00:00",
                "to_date": "2024-01-07 23:59:59",
                "vessel_id": 32,
                "vessel_imo": "8918461",
                "vessel_name": "Wilson Saga"
            }
            mock_rename_vat.return_value = expected_payload
            
            result = await home_logic.home(**params)
        
        # Assert
        mock_rename_vat.assert_called_once()
        # Verify that rename_vat was called with all the parameters
        call_args = mock_rename_vat.call_args[1]
        for key, value in params.items():
            assert call_args[key] == value
    
    @pytest.mark.asyncio
    async def test_home_with_minimal_params(self, home_logic, mock_home_service, mock_measurements, mock_data_controller):
        """Test home method with minimal required parameters."""
        # Setup - only required parameters
        params = {
            "from_date": "2024-01-01 00:00:00",
            "owner_vat": "NO950700939",
            "to_date": "2024-01-07 23:59:59",
            "vessel_id": 32,
            "vessel_imo": "8918461",
            "vessel_name": "Wilson Saga"
        }
        
        service_data = {"measurements": []}
        final_result = {"processed_data": "minimal"}
        
        mock_home_service.home.return_value = service_data
        mock_measurements.get_and_set_measurements.return_value = None
        mock_data_controller.wss_serve.return_value = final_result
        
        # Execute
        with patch('app.modules.home.home_logic.rename_vat') as mock_rename_vat:
            mock_rename_vat.return_value = {"selected_owner_vat": "NO950700939"}
            
            result = await home_logic.home(**params)
        
        # Assert
        assert result == final_result
