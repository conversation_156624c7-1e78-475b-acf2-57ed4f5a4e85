# Anomaly Detection Logic - handles business logic for detecting vessel anomalies

from typing import Any

from app.modules.anomaly_detection.anomaly_detection_service import AnomalyDetectionService
from app.context.utils import rename_vat


class AnomalyDetectionLogic:
    # Logic layer for anomaly detection operations

    def __init__(self, anomaly_detection_service: AnomalyDetectionService):
        self.anomaly_detection_service = anomaly_detection_service

    async def anomaly_detection(self, owner_vat: str, vessel_imo: int) -> Any:
        # Process anomaly detection request - rename VAT parameter and call service
        return await self.anomaly_detection_service.anomaly_detection(rename_vat(**locals()))
