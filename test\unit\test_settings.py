"""
Unit tests for settings configuration.
"""
import os
import pytest
from unittest.mock import patch

from app.config.settings import Settings


class TestSettings:
    """Test cases for Settings class."""
    
    def test_settings_with_all_env_vars(self):
        """Test Settings initialization with all environment variables set."""
        env_vars = {
            "JWT_SECRET": "test-jwt-secret",
            "API_HOST": "***********",
            "API_PORT": "9000",
            "API_RELOAD": "false"
        }
        
        with patch.dict(os.environ, env_vars, clear=True):
            settings = Settings()
            
            assert settings.jwt_secret == "test-jwt-secret"
            assert settings.host == "***********"
            assert settings.port == 9000
            assert settings.reload is False
            assert settings.log_level == "info"
            assert settings.access_log is True
    
    def test_settings_with_minimal_env_vars(self):
        """Test Settings initialization with only required environment variables."""
        env_vars = {
            "JWT_SECRET": "test-jwt-secret"
        }
        
        with patch.dict(os.environ, env_vars, clear=True):
            settings = Settings()
            
            assert settings.jwt_secret == "test-jwt-secret"
            assert settings.host == "0.0.0.0"  # Default value
            assert settings.port == 8769  # Default value
            assert settings.reload is True  # Default value
            assert settings.log_level == "info"
            assert settings.access_log is True
    
    def test_settings_missing_jwt_secret(self):
        """Test Settings initialization without JWT_SECRET raises RuntimeError."""
        with patch.dict(os.environ, {}, clear=True):
            with pytest.raises(RuntimeError, match="Missing required environment variable: JWT_SECRET"):
                Settings()
    
    def test_settings_empty_jwt_secret(self):
        """Test Settings initialization with empty JWT_SECRET raises RuntimeError."""
        env_vars = {"JWT_SECRET": ""}
        
        with patch.dict(os.environ, env_vars, clear=True):
            with pytest.raises(RuntimeError, match="Missing required environment variable: JWT_SECRET"):
                Settings()
    
    def test_settings_port_conversion(self):
        """Test Settings port conversion from string to integer."""
        env_vars = {
            "JWT_SECRET": "test-jwt-secret",
            "API_PORT": "8080"
        }
        
        with patch.dict(os.environ, env_vars, clear=True):
            settings = Settings()
            assert settings.port == 8080
            assert isinstance(settings.port, int)
    
    def test_settings_invalid_port(self):
        """Test Settings with invalid port value raises ValueError."""
        env_vars = {
            "JWT_SECRET": "test-jwt-secret",
            "API_PORT": "invalid-port"
        }
        
        with patch.dict(os.environ, env_vars, clear=True):
            with pytest.raises(ValueError):
                Settings()
    
    def test_settings_reload_true_values(self):
        """Test Settings reload conversion for various true values."""
        true_values = ["true", "True", "TRUE", "1", "yes", "Yes", "YES"]
        
        for value in true_values:
            env_vars = {
                "JWT_SECRET": "test-jwt-secret",
                "API_RELOAD": value
            }
            
            with patch.dict(os.environ, env_vars, clear=True):
                settings = Settings()
                assert settings.reload is True, f"Failed for value: {value}"
    
    def test_settings_reload_false_values(self):
        """Test Settings reload conversion for various false values."""
        false_values = ["false", "False", "FALSE", "0", "no", "No", "NO", ""]
        
        for value in false_values:
            env_vars = {
                "JWT_SECRET": "test-jwt-secret",
                "API_RELOAD": value
            }
            
            with patch.dict(os.environ, env_vars, clear=True):
                settings = Settings()
                assert settings.reload is False, f"Failed for value: {value}"
    
    def test_settings_default_values(self):
        """Test Settings default values when environment variables are not set."""
        env_vars = {"JWT_SECRET": "test-jwt-secret"}
        
        with patch.dict(os.environ, env_vars, clear=True):
            settings = Settings()
            
            # Test default values
            assert settings.host == "0.0.0.0"
            assert settings.port == 8769
            assert settings.reload is True
            assert settings.log_level == "info"
            assert settings.access_log is True
    
    def test_settings_custom_host(self):
        """Test Settings with custom host configuration."""
        env_vars = {
            "JWT_SECRET": "test-jwt-secret",
            "API_HOST": "localhost"
        }
        
        with patch.dict(os.environ, env_vars, clear=True):
            settings = Settings()
            assert settings.host == "localhost"
    
    def test_settings_edge_case_port_values(self):
        """Test Settings with edge case port values."""
        test_cases = [
            ("1", 1),
            ("65535", 65535),
            ("8000", 8000)
        ]
        
        for port_str, expected_port in test_cases:
            env_vars = {
                "JWT_SECRET": "test-jwt-secret",
                "API_PORT": port_str
            }
            
            with patch.dict(os.environ, env_vars, clear=True):
                settings = Settings()
                assert settings.port == expected_port
    
    def test_settings_immutable_properties(self):
        """Test that settings properties are properly set and accessible."""
        env_vars = {
            "JWT_SECRET": "test-jwt-secret",
            "API_HOST": "test-host",
            "API_PORT": "8080",
            "API_RELOAD": "false"
        }
        
        with patch.dict(os.environ, env_vars, clear=True):
            settings = Settings()
            
            # Verify all properties are accessible
            assert hasattr(settings, 'jwt_secret')
            assert hasattr(settings, 'host')
            assert hasattr(settings, 'port')
            assert hasattr(settings, 'reload')
            assert hasattr(settings, 'log_level')
            assert hasattr(settings, 'access_log')
            
            # Verify values
            assert settings.jwt_secret == "test-jwt-secret"
            assert settings.host == "test-host"
            assert settings.port == 8080
            assert settings.reload is False
            assert settings.log_level == "info"
            assert settings.access_log is True


class TestSettingsIntegration:
    """Integration tests for Settings with actual environment."""
    
    def test_settings_with_dotenv_file(self):
        """Test Settings loading from .env file (if present)."""
        # This test would require an actual .env file
        # For now, we'll just test that dotenv loading doesn't break anything
        env_vars = {"JWT_SECRET": "test-jwt-secret"}
        
        with patch.dict(os.environ, env_vars, clear=True):
            # Should not raise any exceptions
            settings = Settings()
            assert settings.jwt_secret == "test-jwt-secret"
    
    def test_settings_singleton_behavior(self):
        """Test that settings behave consistently across imports."""
        env_vars = {"JWT_SECRET": "test-jwt-secret"}
        
        with patch.dict(os.environ, env_vars, clear=True):
            # Import the settings instance
            from app.config.settings import settings
            
            # Create a new Settings instance
            new_settings = Settings()
            
            # They should have the same values
            assert settings.jwt_secret == new_settings.jwt_secret
            assert settings.host == new_settings.host
            assert settings.port == new_settings.port
