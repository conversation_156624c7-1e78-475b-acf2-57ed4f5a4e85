import zlib
from json import dumps, loads
import pandas as pd
from bson import json_util
from datetime import date, datetime

from orjson import orjson

from app.services.redis_api.redis_api_client import RedisApi
from .cii_helper import CiiHelper
from app.modules.cii.cii_service import CiiService


class Cii:
    def __init__(self):
        self.cii_service = CiiService()
        self.redis_api = RedisApi()

    @staticmethod
    def get_redis_object(conn, path: str):
        data = None
        try:
            redis_data = conn.get(path)
            decompressed_data = zlib.decompress(redis_data).decode("utf-8")
            data = loads(decompressed_data, object_hook=json_util.object_hook)
        except Exception as e:
            data = loads(conn.get(path), object_hook=json_util.object_hook)
        finally:
            return data

    @staticmethod
    def json_serial(obj):
        if isinstance(obj, (datetime, date)):
            return datetime.strftime(obj, "%Y-%m-%d %H:%M:%S")
        raise TypeError("Type %s not serializable" % type(obj))

    @staticmethod
    def put_json_object(conn, path: str, _object):
        is_updated = conn.set(
            path, zlib.compress(dumps(_object, default=Cii.json_serial).encode("utf-8"))
        )
        if not is_updated:
            raise IOError("Redis did not update %s correctly - probably redo?", path)

    def convert_updated_fuel_values(self, updated_values):
        for item in updated_values:
            item["fuelConsumption"] = float(item["fuelConsumption"])
        return updated_values

    def check_if_values_is_changed(self, updated_values, frugal_values):
        updated_fuel = self.convert_updated_fuel_values(updated_values.get("fuel"))
        updated_fuel = {
            item["fuelType"]: item["fuelConsumption"] for item in updated_fuel
        }
        frugal_fuel = frugal_values.get("fuel_consumption")
        frugal_fuel = {k: round(v, 2) for k, v in frugal_fuel.items()}
        updated_distance = float(updated_values.get("distance"))
        frugal_distance = round(frugal_values.get("distance"), 2)
        if updated_fuel == frugal_fuel and updated_distance == frugal_distance:
            return True
        else:
            return False

    def convert_actual_week(self, week):
        frugal_fuel = {k: round(v, 2) for k, v in week.get("fuel_consumption").items()}
        frugal_distance = (
            round(week.get("distance"), 2) if week.get("distance") is not None else 0
        )

        return frugal_fuel, frugal_distance

    def check_if_values_has_changed(
        self, updated_fuel_consumption, updated_distance, frugal_fuel, frugal_distance
    ):
        if (
            updated_fuel_consumption == frugal_fuel
            and updated_distance == frugal_distance
        ):
            return True
        else:
            return False

    async def start_calculating_cii_slope(
        self, selected_year, all_weeks_for_selected_year
    ):
        weekly_data = (
            all_weeks_for_selected_year.get("cii_summary")
            .get("years")
            .get(selected_year)
            .get("weekly")
        )
        manual_weekly = (
            all_weeks_for_selected_year.get("cii_summary")
            .get("years")
            .get(selected_year)
            .get("manual_weekly")
        )
        cii_slope = await CiiHelper.calculate_cii_slope(weekly_data, manual_weekly)
        return str(cii_slope)

    async def update_redis_object(
        self,
        prev_redis_value,
        updated_values,
        imo,
        assigned_owner_vat,
        selected_year,
        week_numb,
    ):
        # converting to the right format
        updated_fuel_consumption = self.convert_updated_fuel_values(
            updated_values.get("fuel")
        )
        updated_fuel_consumption = {
            item["fuelType"]: item["fuelConsumption"]
            for item in updated_fuel_consumption
        }
        updated_distance = float(updated_values.get("distance"))

        prev_values = prev_redis_value[0].get("value")
        frugal_fuel, frugal_distance = self.convert_actual_week(prev_values)

        if self.check_if_values_has_changed(
            updated_fuel_consumption, updated_distance, frugal_fuel, frugal_distance
        ):
            return {
                "msg": f"Please change the values if you want to update for week {week_numb}.",
                "status": 304,
            }
        else:
            manual_redis_key = f"vessel_{imo}_{assigned_owner_vat}:manual_cii:{selected_year}:{week_numb}"
            cii_yearly_key = f"vessel_{imo}_{assigned_owner_vat}:cii:{selected_year}"

            updated_values = {
                "start": prev_values.get("start"),
                "end": prev_values.get("end"),
                "fuel_consumption": updated_fuel_consumption,
                "distance": updated_distance,
                "week": week_numb,
                "info": {
                    "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                    "msg": "Based on manual input",
                },
            }

            # calculations for the selected week
            yearly_redis_cii = await self.redis_api.get_key_data(cii_yearly_key)
            yearly_redis_cii = yearly_redis_cii[0].get("value")
            capacity = yearly_redis_cii["capacity"]
            cii_snapshot, emissions = self.calculate_weekly_cii(
                updated_values, capacity
            )
            updated_values["cii_snapshot"] = cii_snapshot
            updated_values["emissions"] = emissions

            await self.cii_service.set_json_data(
                {"key": manual_redis_key, "value": updated_values}
            )

            # new yearly calculations
            payload = {
                "vessel_imo": imo,
                "selected_owner_vat": assigned_owner_vat,
                "year": selected_year,
            }
            all_weeks_for_selected_year = await self.cii_service.get_cii_data(payload)
            cii_slope = await self.start_calculating_cii_slope(
                selected_year, all_weeks_for_selected_year
            )

            # yearly calculations
            yearly_calculations = await self.calculate_yearly_cii(
                all_weeks_for_selected_year, selected_year, imo, assigned_owner_vat
            )
            yearly_calculations["cii"]["cii_slope"] = cii_slope
            await self.cii_service.set_json_data(
                {
                    "key": f"vessel_{imo}_{assigned_owner_vat}:cii:{selected_year}",
                    "value": yearly_calculations,
                }
            )
            all_weeks_for_selected_year["cii_summary"]["years"][selected_year][
                "yearly"
            ] = yearly_calculations
            return all_weeks_for_selected_year

    async def get_latest_row_for_week(self, key, week_numb):
        try:
            weekly_cii_log = await self.redis_api.get_key_data(key)
            weekly_cii_log = weekly_cii_log[0].get("value")
            if weekly_cii_log is not None and len(weekly_cii_log) > 0:
                return {
                    "prev_values": weekly_cii_log[-1].get("updated_values"),
                    "prev_update_timestamp": weekly_cii_log[-1].get("update_timestamp"),
                }
            else:
                return {"prev_values": None, "prev_update_timestamp": None}
        except Exception:
            return {
                "msg": f"Could not get latest row for week {week_numb}",
                "status": 500,
            }

    async def insert_log(
        self,
        updated_values,
        frugal_values,
        imo,
        assigned_owner_vat,
        selected_year,
        vessel_id,
    ):
        week_numb = updated_values.get("week_numb")

        if updated_values is None and frugal_values is None:
            raise ValueError("Data is mandatory.")
        else:
            try:
                weekly_cii_log_key = f"vessel_{imo}_{assigned_owner_vat}:cii:{selected_year}:cii_log:{week_numb}"
                prev_values = await self.get_latest_row_for_week(
                    weekly_cii_log_key, week_numb
                )
                current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

                weekly_cii_log = await self.redis_api.get_key_data(weekly_cii_log_key)
                weekly_cii_log = weekly_cii_log[0].get("value")

                log = {
                    "update_timestamp": current_time,
                    "vessel_id": vessel_id,
                    "week_numb": week_numb,
                    "year": selected_year,
                    "frugal_values": frugal_values,
                    "updated_values": updated_values,
                    "prev_values": prev_values.get("prev_values"),
                    "prev_update_timestamp": prev_values.get("prev_update_timestamp"),
                }

                if weekly_cii_log is None:
                    log_list = []
                    log_list.append(log)
                    await self.cii_service.set_json_data(
                        {"key": weekly_cii_log_key, "value": log_list}
                    )
                else:
                    weekly_cii_log.append(log)
                    await self.cii_service.set_json_data(
                        {"key": weekly_cii_log_key, "value": weekly_cii_log}
                    )

            except Exception:
                return {
                    "msg": f"Could not update cii for year {selected_year} and week {week_numb}",
                    "status": 304,
                }
            return {
                "msg": f"Updated cii for year {selected_year} and week {week_numb} successfully.",
                "status": 204,
            }

    async def cii_update(self, updated_values):
        selected_year = updated_values.get("selected_year")
        week_numb = updated_values.get("week_numb")
        imo = updated_values.get("selected_vessel").get("imo")
        vessel_id = updated_values.get("selected_vessel").get("id")
        assigned_owner_vat = updated_values.get("selected_vessel").get(
            "assigned_owner_vat"
        )
        redis_key = f"vessel_{imo}_{assigned_owner_vat}:cii:{selected_year}"
        prev_redis_value = await self.redis_api.get_key_data(
            redis_key + f":{week_numb}"
        )
        vessel_redis = await self.update_redis_object(
            prev_redis_value,
            updated_values,
            imo,
            assigned_owner_vat,
            selected_year,
            week_numb,
        )

        if "msg" not in vessel_redis:
            frugal_values = await self.redis_api.get_key_data(
                f"vessel_{imo}_{assigned_owner_vat}:cii:{selected_year}:{week_numb}"
            )
            frugal_values = frugal_values[0].get("value")
            response = await self.insert_log(
                updated_values,
                frugal_values,
                imo,
                assigned_owner_vat,
                selected_year,
                vessel_id,
            )
            response["redis"] = vessel_redis
            return response
        return {
            "msg": f"Please change the values if you want to update for week {week_numb}.",
            "status": 304,
        }

    async def cii_reset(self, vessel_imo, year, owner_vat, week_numb=None):
        if week_numb:
            manual_key = f"vessel_{vessel_imo}_{owner_vat}:manual_cii:{year}:{week_numb}"

            status = await self.cii_service.delete_key(manual_key)
            payload = {
                "vessel_imo": vessel_imo,
                "selected_owner_vat": owner_vat,
                "year": year,
            }
            all_weeks_for_selected_year = await self.cii_service.get_cii_data(payload)
            cii_slope = await self.start_calculating_cii_slope(
                year, all_weeks_for_selected_year
            )
            all_weeks_for_selected_year["cii_summary"]["years"][year]["yearly"]["cii"][
                "cii_slope"
            ] = cii_slope

            yearly_calculations = await self.calculate_yearly_cii(
                all_weeks_for_selected_year, year, vessel_imo, owner_vat
            )
            await self.cii_service.set_json_data(
                {
                    "key": f"vessel_{vessel_imo}_{owner_vat}:cii:{year}",
                    "value": yearly_calculations,
                }
            )
            all_weeks_for_selected_year["cii_summary"]["years"][year]["yearly"] = (
                yearly_calculations
            )
            msg = f"The values for week {week_numb} have been successfully reset."
            if status == "SUCCESS":
                return {"msg": msg, "status": 204, "redis_api": all_weeks_for_selected_year}
        elif week_numb is None:
            payload = {
                "vessel_imo": vessel_imo,
                "selected_owner_vat": owner_vat,
                "year": year,
            }
            all_weeks_for_selected_year = await self.cii_service.get_cii_data(payload)
            manual_weekly = (
                all_weeks_for_selected_year.get("cii_summary")
                .get("years")
                .get(year)
                .get("manual_weekly")
            )
            if not manual_weekly.keys():
                return {
                    "msg": f"The values for year {year} have already been reset",
                    "status": 304,
                    "redis_api": None,
                }
            else:
                for key in manual_weekly.keys():
                    manual_key = (
                        f"vessel_{vessel_imo}_{owner_vat}:manual_cii:{year}:{key}"
                    )
                    await self.cii_service.delete_key(manual_key)
                payload = {
                    "vessel_imo": vessel_imo,
                    "selected_owner_vat": owner_vat,
                    "year": year,
                }
                all_weeks_for_selected_year = await self.cii_service.get_cii_data(payload)
                cii_slope = await self.start_calculating_cii_slope(
                    year, all_weeks_for_selected_year
                )
                all_weeks_for_selected_year["cii_summary"]["years"][year]["yearly"][
                    "cii"
                ]["cii_slope"] = cii_slope

                yearly_calculations = await self.calculate_yearly_cii(
                    all_weeks_for_selected_year, year, vessel_imo, owner_vat
                )
                await self.cii_service.set_json_data(
                    {
                        "key": f"vessel_{vessel_imo}_{owner_vat}:cii:{year}",
                        "value": yearly_calculations,
                    }
                )
                all_weeks_for_selected_year["cii_summary"]["years"][year]["yearly"] = (
                    yearly_calculations
                )
                msg = f"The values for year {year} have been successfully reset."
                return {"msg": msg, "status": 204, "redis_api": all_weeks_for_selected_year}

        return {
            "msg": "The values have already been reset.",
            "status": 304,
            "redis_api": None,
        }

    def calculate_weekly_cii(self, updated_values, capacity):
        emissions = CiiHelper.get_emissions(updated_values.get("fuel_consumption"))
        cii_snapshot = CiiHelper.calculate_attained_cii(
            updated_values.get("distance"), emissions, capacity.get("capacity")
        )
        return cii_snapshot, emissions

    async def get_cii_log(self, vessel_imo, week_numb, year, vat):
        weekly_cii_log = f"vessel_{vessel_imo}_{vat}:cii:{year}:cii_log:{week_numb}"
        response = await self.redis_api.get_key_data(weekly_cii_log)
        response = response[0].get("value")

        if response is not None:
            for item in response:
                if item.get("prev_values") is None:
                    item["prev_values"] = item.get("frugal_values")

            return response

    async def calculate_yearly_cii(
        self, all_weeks_for_selected_year, selected_year, imo, assigned_owner_vat
    ):
        selected_year_data = (
            all_weeks_for_selected_year.get("cii_summary")
            .get("years")
            .get(selected_year)
        )
        yearly_overview = selected_year_data.get("yearly")
        weekly = selected_year_data.get("weekly")
        capacity = selected_year_data.get("yearly").get("capacity").get("capacity")
        manual_weekly = (
            all_weeks_for_selected_year.get("cii_summary")
            .get("years")
            .get(selected_year)
            .get("manual_weekly")
        )
        manual_weekly_keys = list(manual_weekly.keys())

        total_emissions = 0
        total_fuel = pd.DataFrame()
        total_distance = 0

        for week in weekly.items():
            week_numb = week[0]
            weekly_results = week[1]

            if week_numb not in manual_weekly_keys:
                total_emissions += weekly_results.get("emissions")
                df = pd.DataFrame(
                    list(weekly_results.get("fuel_consumption").items()),
                    columns=["fuel_type", "value"],
                )
                total_fuel = pd.concat([total_fuel, df], ignore_index=True)
                total_distance += (
                    weekly_results.get("distance")
                    if weekly_results.get("distance") is not None
                    else 0
                )
            else:
                total_emissions += manual_weekly.get(week_numb).get("emissions")
                df = pd.DataFrame(
                    list(manual_weekly.get(week_numb).get("fuel_consumption").items()),
                    columns=["fuel_type", "value"],
                )
                total_fuel = pd.concat([total_fuel, df], ignore_index=True)
                total_distance += manual_weekly.get(week_numb).get("distance")
        total_fuel = total_fuel.groupby("fuel_type")["value"].sum()
        attained_cii = CiiHelper.calculate_attained_cii(
            total_distance,
            total_emissions,
            capacity,
        )

        yearly_overview["cii"]["attained_cii"] = attained_cii
        yearly_overview["cii"]["cii_letter_rating"] = CiiHelper.get_letter_cii_rating(
            attained_cii, yearly_overview["cii"]["adjusted_boundaries"]
        )

        yearly_overview["fuel_consumption"] = total_fuel.to_dict()
        yearly_overview["emissions"] = total_emissions
        yearly_overview["distance"] = total_distance

        vessel_info_key = f"vessel_{imo}_{assigned_owner_vat}:info"
        vessel_info = await self.redis_api.get_key_data(vessel_info_key)

        cii_ref_key = f"vessel_{imo}_{assigned_owner_vat}:cii_ref"
        cii_ref = await self.redis_api.get_key_data(cii_ref_key)
        cii_ref = cii_ref[0].get("value")
        if vessel_info is not None and cii_ref is not None:
            vessel_info = vessel_info[0].get("value")

            dead_weight, gross_tonnage, vessel_type = (
                vessel_info.get("dead_weight"),
                vessel_info.get("gross_tonnage"),
                CiiHelper.format_vessel_type_from_db(vessel_info.get("vessel_type")),
            )

            projected_cii = CiiHelper.project_cii(
                yearly_overview, capacity, vessel_type, int(selected_year), cii_ref
            )
            yearly_overview["cii"]["projected_cii"] = projected_cii

            return yearly_overview
