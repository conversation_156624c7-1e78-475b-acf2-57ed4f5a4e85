"""
Integration tests for module interactions and workflows.
"""
import pytest
import asyncio
from unittest.mock import patch, AsyncMock

from app.modules.home.home_logic import HomeLogic
from app.modules.home.home_service import HomeService
from app.modules.user.user_logic import UserLogic
from app.modules.user.user_service import UserService
from app.general_logic_helper.controllers.data_controller import DataController
from app.general_logic_helper.handlers.measurements import Measurements


@pytest.mark.integration
class TestHomeModuleWorkflow:
    """Integration tests for complete Home module workflow."""
    
    @pytest.fixture
    def mock_dependencies(self):
        """Mock all dependencies for Home module."""
        return {
            "home_service": AsyncMock(spec=HomeService),
            "data_controller": AsyncMock(spec=DataController),
            "measurements": AsyncMock(spec=Measurements)
        }
    
    @pytest.mark.asyncio
    async def test_complete_home_workflow(self, mock_dependencies):
        """Test complete workflow from router to service."""
        # Setup mock responses
        service_data = {
            "vessel_info": {"name": "Wilson Saga", "imo": "8918461"},
            "measurements": [
                {"timestamp": "2024-01-01T00:00:00Z", "fuel_consumption": 100}
            ]
        }
        
        processed_data = {
            "vessel_info": service_data["vessel_info"],
            "processed_measurements": service_data["measurements"],
            "analytics": {"efficiency_score": 85}
        }
        
        mock_dependencies["home_service"].home.return_value = service_data
        mock_dependencies["measurements"].get_and_set_measurements.return_value = None
        mock_dependencies["data_controller"].wss_serve.return_value = processed_data
        
        # Create logic instance
        home_logic = HomeLogic(
            mock_dependencies["home_service"],
            mock_dependencies["data_controller"],
            mock_dependencies["measurements"]
        )
        
        # Execute workflow
        with patch('app.modules.home.home_logic.rename_vat') as mock_rename_vat:
            mock_rename_vat.return_value = {
                "selected_owner_vat": "NO950700939",
                "vessel_imo": "8918461"
            }
            
            result = await home_logic.home(
                owner_vat="NO950700939",
                vessel_imo="8918461",
                vessel_id=32,
                vessel_name="Wilson Saga",
                from_date="2024-01-01 00:00:00",
                to_date="2024-01-07 23:59:59"
            )
        
        # Verify workflow execution
        assert result == processed_data
        mock_dependencies["home_service"].home.assert_called_once()
        mock_dependencies["measurements"].get_and_set_measurements.assert_called_once()
        mock_dependencies["data_controller"].wss_serve.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_home_workflow_with_redis_integration(self):
        """Test Home workflow with actual Redis service integration."""
        with patch('app.services.redis_api.redis_api_client.aioredis') as mock_aioredis:
            mock_connection = AsyncMock()
            mock_aioredis.from_url.return_value = mock_connection
            
            # Mock Redis responses
            redis_response = {
                "home_page_data": {
                    "vessel_info": {"name": "Wilson Saga"},
                    "measurements": []
                }
            }
            
            mock_http_response = AsyncMock()
            mock_http_response.json.return_value = redis_response
            mock_http_response.status_code = 200
            
            with patch('httpx.AsyncClient.request', return_value=mock_http_response):
                # Create real service with mocked Redis
                home_service = HomeService()
                
                # Mock other dependencies
                mock_data_controller = AsyncMock(spec=DataController)
                mock_measurements = AsyncMock(spec=Measurements)
                
                mock_data_controller.wss_serve.return_value = {"final": "result"}
                mock_measurements.get_and_set_measurements.return_value = None
                
                # Create logic with real service
                home_logic = HomeLogic(home_service, mock_data_controller, mock_measurements)
                
                # Execute workflow
                with patch('app.modules.home.home_logic.rename_vat') as mock_rename_vat:
                    mock_rename_vat.return_value = {"selected_owner_vat": "NO950700939"}
                    
                    result = await home_logic.home(
                        owner_vat="NO950700939",
                        vessel_imo="8918461",
                        vessel_id=32,
                        vessel_name="Wilson Saga",
                        from_date="2024-01-01 00:00:00",
                        to_date="2024-01-07 23:59:59"
                    )
                
                assert result == {"final": "result"}


@pytest.mark.integration
class TestUserModuleWorkflow:
    """Integration tests for complete User module workflow."""
    
    @pytest.mark.asyncio
    async def test_user_workflow_with_service_integration(self):
        """Test User workflow with service integration."""
        with patch('app.services.redis_api.redis_api_client.aioredis') as mock_aioredis:
            mock_connection = AsyncMock()
            mock_aioredis.from_url.return_value = mock_connection
            
            # Mock Redis response for users
            users_response = {
                "users": [
                    {"id": 1, "email": "<EMAIL>", "name": "User One"},
                    {"id": 2, "email": "<EMAIL>", "name": "User Two"}
                ]
            }
            
            mock_http_response = AsyncMock()
            mock_http_response.json.return_value = users_response
            mock_http_response.status_code = 200
            
            with patch('httpx.AsyncClient.request', return_value=mock_http_response):
                # Create real service with mocked Redis
                user_service = UserService()
                user_logic = UserLogic(user_service)
                
                # Execute workflow
                result = await user_logic.users(owner_vat="NO950700939")
                
                assert result == users_response
                assert len(result["users"]) == 2


@pytest.mark.integration
class TestCrossModuleInteractions:
    """Integration tests for interactions between different modules."""
    
    @pytest.mark.asyncio
    async def test_home_and_user_module_interaction(self):
        """Test interaction between Home and User modules."""
        with patch('app.services.redis_api.redis_api_client.aioredis') as mock_aioredis:
            mock_connection = AsyncMock()
            mock_aioredis.from_url.return_value = mock_connection
            
            # Mock different responses for different endpoints
            responses = [
                AsyncMock(json=AsyncMock(return_value={"home_page_data": {"vessel_info": {}}}), status_code=200),
                AsyncMock(json=AsyncMock(return_value={"users": [{"id": 1}]}), status_code=200)
            ]
            
            with patch('httpx.AsyncClient.request', side_effect=responses):
                # Create services
                home_service = HomeService()
                user_service = UserService()
                
                # Create logic instances
                home_logic = HomeLogic(home_service, AsyncMock(), AsyncMock())
                user_logic = UserLogic(user_service)
                
                # Execute concurrent operations
                with patch('app.modules.home.home_logic.rename_vat') as mock_rename_vat:
                    mock_rename_vat.return_value = {"selected_owner_vat": "NO950700939"}
                    
                    tasks = [
                        home_logic.owners_with_vessel(owner_vat="NO950700939"),
                        user_logic.users(owner_vat="NO950700939")
                    ]
                    
                    results = await asyncio.gather(*tasks)
                    
                    # Verify both operations completed
                    assert len(results) == 2
                    assert "home_page_data" in results[0] or "owners" in results[0]
                    assert "users" in results[1]
    
    @pytest.mark.asyncio
    async def test_data_flow_between_modules(self):
        """Test data flow and transformation between modules."""
        # Mock a scenario where one module's output feeds into another
        with patch('app.services.redis_api.redis_api_client.aioredis') as mock_aioredis:
            mock_connection = AsyncMock()
            mock_aioredis.from_url.return_value = mock_connection
            
            # Step 1: Get vessel data from Home module
            vessel_data = {
                "owners": [
                    {
                        "vat": "NO950700939",
                        "vessels": [
                            {"imo": "8918461", "name": "Wilson Saga", "id": 32}
                        ]
                    }
                ]
            }
            
            # Step 2: Use vessel info for detailed analytics
            analytics_data = {
                "vessel_analytics": {
                    "imo": "8918461",
                    "efficiency_score": 85,
                    "fuel_consumption": 1000
                }
            }
            
            responses = [
                AsyncMock(json=AsyncMock(return_value=vessel_data), status_code=200),
                AsyncMock(json=AsyncMock(return_value=analytics_data), status_code=200)
            ]
            
            with patch('httpx.AsyncClient.request', side_effect=responses):
                home_service = HomeService()
                
                # Step 1: Get vessel information
                vessels_result = await home_service.owners_with_vessel(owner_vat="NO950700939")
                
                # Extract vessel IMO for next step
                vessel_imo = vessels_result["owners"][0]["vessels"][0]["imo"]
                
                # Step 2: Get analytics for the vessel
                # (This would typically be done by another service)
                analytics_payload = {
                    "vessel_imo": vessel_imo,
                    "owner_vat": "NO950700939"
                }
                
                # Simulate analytics call
                analytics_result = await home_service.home(analytics_payload)
                
                # Verify data flow
                assert vessel_imo == "8918461"
                assert analytics_result == analytics_data


@pytest.mark.integration
class TestErrorPropagation:
    """Integration tests for error propagation across modules."""
    
    @pytest.mark.asyncio
    async def test_service_error_propagation_to_logic(self):
        """Test how service errors propagate to logic layer."""
        # Create service that raises an error
        mock_service = AsyncMock(spec=HomeService)
        mock_service.home.side_effect = ConnectionError("Redis connection failed")
        
        # Create logic with failing service
        home_logic = HomeLogic(mock_service, AsyncMock(), AsyncMock())
        
        # Verify error propagates
        with patch('app.modules.home.home_logic.rename_vat') as mock_rename_vat:
            mock_rename_vat.return_value = {"selected_owner_vat": "NO950700939"}
            
            with pytest.raises(ConnectionError, match="Redis connection failed"):
                await home_logic.home(
                    owner_vat="NO950700939",
                    vessel_imo="8918461",
                    vessel_id=32,
                    vessel_name="Wilson Saga",
                    from_date="2024-01-01 00:00:00",
                    to_date="2024-01-07 23:59:59"
                )
    
    @pytest.mark.asyncio
    async def test_partial_failure_handling(self):
        """Test handling of partial failures in multi-step workflows."""
        with patch('app.services.redis_api.redis_api_client.aioredis') as mock_aioredis:
            mock_connection = AsyncMock()
            mock_aioredis.from_url.return_value = mock_connection
            
            # First call succeeds, second fails
            responses = [
                AsyncMock(json=AsyncMock(return_value={"data": "success"}), status_code=200),
                AsyncMock(json=AsyncMock(side_effect=Exception("Network error")), status_code=500)
            ]
            
            with patch('httpx.AsyncClient.request', side_effect=responses):
                home_service = HomeService()
                user_service = UserService()
                
                # First operation should succeed
                result1 = await home_service.owners_with_vessel(owner_vat="NO950700939")
                assert result1 == {"data": "success"}
                
                # Second operation should fail
                with pytest.raises(Exception, match="Network error"):
                    await user_service.users(owner_vat="NO950700939")


@pytest.mark.integration
class TestModulePerformance:
    """Integration tests for module performance characteristics."""
    
    @pytest.mark.asyncio
    async def test_concurrent_module_operations(self):
        """Test performance of concurrent operations across modules."""
        with patch('app.services.redis_api.redis_api_client.aioredis') as mock_aioredis:
            mock_connection = AsyncMock()
            mock_aioredis.from_url.return_value = mock_connection
            
            # Mock fast responses
            mock_response = AsyncMock()
            mock_response.json.return_value = {"result": "fast"}
            mock_response.status_code = 200
            
            with patch('httpx.AsyncClient.request', return_value=mock_response):
                # Create multiple services
                services = [
                    HomeService(),
                    UserService(),
                    HomeService(),  # Test multiple instances
                ]
                
                # Create concurrent tasks
                tasks = [
                    services[0].owners_with_vessel(owner_vat="NO950700939"),
                    services[1].users(owner_vat="NO950700939"),
                    services[2].home({"selected_owner_vat": "NO950700939"})
                ]
                
                # Measure execution time
                import time
                start_time = time.time()
                
                results = await asyncio.gather(*tasks)
                
                end_time = time.time()
                execution_time = end_time - start_time
                
                # Verify all operations completed
                assert len(results) == 3
                for result in results:
                    assert result == {"result": "fast"}
                
                # Should complete quickly due to concurrency
                assert execution_time < 2.0  # 2 seconds threshold
    
    @pytest.mark.asyncio
    async def test_module_memory_efficiency(self):
        """Test memory efficiency of module operations."""
        with patch('app.services.redis_api.redis_api_client.aioredis') as mock_aioredis:
            mock_connection = AsyncMock()
            mock_aioredis.from_url.return_value = mock_connection
            
            # Simulate processing large datasets
            large_dataset = [{"item": f"data_{i}"} for i in range(1000)]
            mock_response = AsyncMock()
            mock_response.json.return_value = {"large_data": large_dataset}
            mock_response.status_code = 200
            
            with patch('httpx.AsyncClient.request', return_value=mock_response):
                home_service = HomeService()
                
                # Process large dataset multiple times
                results = []
                for i in range(5):
                    result = await home_service.home({"batch": i})
                    results.append(result)
                
                # Verify data integrity
                assert len(results) == 5
                for result in results:
                    assert len(result["large_data"]) == 1000
