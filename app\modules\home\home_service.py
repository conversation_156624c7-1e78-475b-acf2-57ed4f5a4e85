# Home Service - handles data access for home dashboard operations

from app.services.redis_api.redis_api_client import RedisApi


class HomeService:
    # Service layer for home dashboard data operations

    def __init__(self, redis_client: RedisApi = None):
        # Initialize with Redis client (create new one if not provided)
        self.redis = redis_client or RedisApi()

    async def owners_with_vessel(self, **params):
        # Get owner fleet data from Redis API
        return await self.redis.call(
            path="get_owner_fleet_data",
            **params,
        )

    async def home(self, payload):
        # Get home dashboard data from Redis API
        return await self.redis.call(
            path="home",
            key="home_page_data",
            method="POST",
            params=None,
            json=payload,
        )
