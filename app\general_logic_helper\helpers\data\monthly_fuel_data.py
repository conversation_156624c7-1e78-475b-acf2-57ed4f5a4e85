from datetime import datetime


class MonthlyFuelDataHelper:  # TODO Move this logic to Redis Task
    @staticmethod
    def generate_monthly_fuel_data(data, unit, _vessel_id):
        if data is None or data.get("reports").get("monthly_fuel_report") is None:
            return
        monthly_fuel_report = data.get("reports").get("monthly_fuel_report")
        x_val = "Tons"
        me_chart_data = []
        aux_chart_data = []
        boiler_chart_data = []
        me_chart = []
        aux_chart = []
        boiler_chart = []
        today_date = datetime.now()
        day = str(today_date.strftime("%d"))  # day
        month = today_date.strftime("%B")  # month with capitalized first letter
        if month in monthly_fuel_report and day in monthly_fuel_report[month]["Days"]:
            monthly_fuel_report[month]["Days"][day]["bf"] = "Day Delay"
        reports = monthly_fuel_report

        for report in reports:
            me_chart_data.append(reports[report]["ME"][unit])
            aux_chart_data.append(reports[report]["AUX"][unit])
            boiler_chart_data.append(reports[report]["Boiler"][unit])
        # Christiania Shipping asked us to fill in data for these two days due to missing data.
        # Jette Theresa.
        if _vessel_id == 14:
            september_report = reports.get("September")
            if september_report is not None:
                day_27 = september_report.get("Days").get("27")
                day_28 = september_report.get("Days").get("28")
                if day_27 is not None:
                    september_report["Days"]["27"]["ME"]["Tons"] += 8.04
                    september_report["Days"]["27"]["ls"] = 9.6
                    september_report["Days"]["27"]["PctData"] = 100.0
                if day_28 is not None:
                    september_report["Days"]["28"]["ME"]["Tons"] += 5.21
                    september_report["Days"]["28"]["ls"] = 9.6
                    september_report["Days"]["28"]["PctData"] = 100.0
            september_report["PctData"] = 99.0
        me_chart.append({"name": x_val, "data": me_chart_data})
        aux_chart.append({"name": x_val, "data": aux_chart_data})
        boiler_chart.append({"name": x_val, "data": boiler_chart_data})
        me_chart[0]["data"].reverse()
        aux_chart[0]["data"].reverse()
        boiler_chart[0]["data"].reverse()
        data["reports"]["monthly_fuel_report_me_data"] = me_chart
        data["reports"]["monthly_fuel_report_aux_data"] = aux_chart
        data["reports"]["monthly_fuel_report_boiler_data"] = boiler_chart

    @staticmethod
    def generate_months_to_use(data, _vessel_id):
        if data is None or data.get("reports").get("monthly_fuel_report") is None:
            return
        monthly_fuel_report = data.get("reports").get("monthly_fuel_report")
        month_data = []
        reports = monthly_fuel_report
        for month in reports:
            if not any(
                substring in month
                for substring in ["me_data", "aux_data", "boiler_data"]
            ):
                month_data.append(month)
        month_data.reverse()
        data["reports"]["months_to_use"] = month_data
