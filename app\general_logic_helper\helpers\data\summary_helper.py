from datetime import datetime


def validate_timestamp(timestamp):
    ts = None
    if isinstance(timestamp, datetime):
        ts = timestamp
    elif isinstance(timestamp, str):
        try:
            # Attempt to parse as an ISO 8601 formatted string
            ts = datetime.fromisoformat(timestamp)
        except ValueError:
            # Handle the case where the string is not ISO 8601
            ts = datetime.strptime(timestamp, "%Y-%m-%d %H:%M:%S")
    return ts


class SummaryHelper:
    @staticmethod
    async def init_data(redis_data):
        vessel_ais_data = (
            redis_data.get("latest_ais_data")
            if redis_data.get("latest_ais_data") is not None
            and len(redis_data.get("latest_ais_data")) > 0
            else None
        )
        latest_entry = (
            redis_data.get("latest_row")[0]
            if redis_data.get("latest_row") is not None
            and len(redis_data.get("latest_row")) > 0
            else None
        )
        status_temp = (
            latest_entry["frugal_status"] if latest_entry is not None else None
        )
        port_eta = None

        if status_temp == 0:
            status = "Disengaged"
        else:
            if status_temp is not None:
                status = "Engaged"
            else:
                status = "Unknown"

        if vessel_ais_data is not None and vessel_ais_data["eta_utc"] is not None:
            vessel_eta = datetime.strftime(
                validate_timestamp(vessel_ais_data["eta_utc"]), "%Y-%m-%d %H:%M:%S"
            )
            if vessel_ais_data["dest_port_name"] is not None:
                port_eta = ", " + vessel_ais_data["dest_port_name"]
            elif vessel_ais_data["destination"] is not None:
                port_eta = ", " + vessel_ais_data["destination"]
            else:
                port_eta += ", Unknown"
            if vessel_ais_data["dest_port_unlo"] is not None:
                port_eta += " (" + vessel_ais_data["dest_port_unlo"] + ")"
        else:
            vessel_eta = "Unavailable.."
            port_eta = ""

        gps_speed = latest_entry["gps_speed"] if latest_entry is not None else None

        converted_eta_to_percentage = SummaryHelper.convert_epoch_to_date_UTC(
            vessel_ais_data
        )
        percentage = 0

        if vessel_ais_data is not None and converted_eta_to_percentage is not None:
            percentage = converted_eta_to_percentage

        redis_data["summaryData"] = {
            "percentage": percentage,
            "latest_timestamp": latest_entry["timestamp"]
            if latest_entry is not None
            else None,
            "status": status,
            "vessel_eta": vessel_eta,
            "port_eta": port_eta,
            "gps_speed": gps_speed,
        }

    @staticmethod
    def convert_epoch_to_date_UTC(vessel_ais_data):
        percentage = 0
        if vessel_ais_data is not None:
            atd_epoch = vessel_ais_data.get("atd_epoch")
            eta_epoch = vessel_ais_data.get("eta_epoch")
            if (
                atd_epoch is not None
                and atd_epoch != 0
                and eta_epoch is not None
                and eta_epoch != 0
            ):
                start_date = datetime.fromtimestamp(atd_epoch)
                end_date = datetime.fromtimestamp(eta_epoch)
                percentage = (
                    (datetime.now() - start_date) / (end_date - start_date)
                ) * 100

                if percentage > 100:
                    percentage = 100
                elif percentage < 0:
                    percentage = 0

        return percentage
