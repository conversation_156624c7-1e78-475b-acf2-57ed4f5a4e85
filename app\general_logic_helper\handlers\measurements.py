from frugaltech_utilities.configuration_helper import ConfigurationHelper
import logging

from app.services.redis_api.redis_api_client import RedisApi


class Measurements:
    def __init__(self):
        self.config = ConfigurationHelper().instance()
        self.redis_api = RedisApi()
        self.debug = self.config.getboolean("general", "do-debug")

    async def get_and_set_measurements(self, request, redis_data):
        try:
            request["type"] = "measurements"
            measurements_result = await self.redis_api.retrieve_time_series_data(request)
            if not measurements_result:
                redis_data.update({"measurements": {}})
            else:
                measurements = measurements_result.get("measurements")
                redis_data.update({"measurements": measurements})
        except Exception as e:
            logging.error(e)
