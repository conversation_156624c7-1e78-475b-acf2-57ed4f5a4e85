# MRV Logic - handles business logic for MRV (Monitoring, Reporting, Verification) operations

from fastapi import HTTPException, Body

from app.modules.mrv.logic_helpers.mrv_helper import MrvHelper
from app.modules.mrv.mrv_service import MrvService
from app.context.utils import rename_vat


class MrvLogic:
    # Logic layer for MRV (Monitoring, Reporting, Verification) operations

    def __init__(self, mrv_service: MrvService, mrv_helper: MrvHelper):
        self.mrv_service = mrv_service
        self.mrv_helper = mrv_helper
        self.fuel_types = ["hfo", "lfo", "mdo"]  # Supported fuel types

    async def voyage_report(self, year: str, vessel_imo: str, owner_vat: str, voyage_id: str, vessel_name: str):
        # Generate PDF report for a specific voyage
        voyage_data = await self.mrv_service.get_voyage_data(**rename_vat(locals(), "year", "vessel_imo", "owner_vat", "voyage_id"))
        flagged_voyages = self.mrv_helper.flag_voyages({1: voyage_data})
        filtered_voyages = self.mrv_helper.filter_complete(flagged_voyages)
        if not filtered_voyages:  # No complete voyages found
            raise Exception("Error: 403")
        return self.mrv_helper.create_pdf_report(voyage_data, vessel_name, vessel_imo)

    async def create_voyage(self, vessel_imo: str, owner_vat: str, year: str,
                            voyage_data: dict = Body(...)):
        # Create new voyage record
        return await self.mrv_service.create_voyage(rename_vat(**locals()))

    async def update_voyage(self,  vessel_imo: str, owner_vat: str, year: str, voyage_id: int,
                            voyage_data: dict = Body(...)):
        # Update existing voyage record
        return await self.mrv_service.update_voyage(rename_vat(**locals()))

    async def delete_voyage(self, owner_vat: str, vessel_imo: str, year: str, voyage_id: str):
        # Delete voyage record
        response = await self.mrv_service.delete_voyage(**rename_vat(**locals()))
        return response

    async def fetch_data(self, vessel_imo: str, owner_vat: str, year: str):
        # Fetch raw MRV data from service
        mrv_data = await self.mrv_service.get_data(**rename_vat(**locals()))
        return mrv_data

    async def mrv_data(self, vessel_imo: str, owner_vat: str, year: str):
        try:
            data = await self.fetch_data(vessel_imo, owner_vat, year)
            original, merged = self.mrv_helper.merge_voyages(data["mrv_data"], data["mrv_data_copy"])
            merged = self.mrv_helper.annotate_voyage_statuses(merged)
            relevant_original = self.mrv_helper.get_converted_data(original)
            relevant_edited = self.mrv_helper.get_converted_data(merged)
            json_orig = self.mrv_helper.convert_to_json(relevant_original)
            json_edit = self.mrv_helper.convert_to_json(relevant_edited)
            return self.mrv_helper.get_final_object(json_orig, json_edit, year)
        except Exception as e:
            if str(e) == "Error: 404":
                raise HTTPException(
                    status_code=404,
                    detail="No MRV related voyages found for this vessel."
                )
            else:
                raise HTTPException(
                    status_code=500,
                    detail=f"An unexpected error occurred: {e}"
                )

    async def mrv_report(self, vessel_imo: str, owner_vat: str, year: str, vessel_name: str):
        mrv_data = await self.data(vessel_imo, owner_vat, year)
        total_mrv_count = len(mrv_data["mrv_data_copy"])
        flagged_updated = self.mrv_helper.flag_voyages(mrv_data["mrv_data_copy"])
        mrv_data['mrv_data_copy'] = self.mrv_helper.filter_complete(flagged_updated)
        filtered_mrv_count = len(mrv_data["mrv_data_copy"])
        if not mrv_data['mrv_data_copy']:  # same as len(filtered_voyages) == 0
            raise Exception("Error: 403")
        report = self.mrv_helper.generate_report(vessel_imo, vessel_name, mrv_data, total_mrv_count, filtered_mrv_count)
        return report
