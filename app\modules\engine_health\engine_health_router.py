# Engine Health Router - API endpoints for engine health monitoring

from fastapi import APIRouter, Depends
from auth import owner_vat_check

from app.modules.engine_health.engine_health_logic import EngineHealthLogic
from app.modules.engine_health.engine_health_service import EngineHealthService
from app.context.context import get_context

# Create router with engine health tag
router = APIRouter(tags=["Engine Health"])

# Set up dependency injection for logic and service layers
get_engine_health_params, get_engine_health_logic = get_context(
    EngineHealthLogic,
    EngineHealthService,
)


@router.get("/engine-health", dependencies=[Depends(owner_vat_check)])
async def engine_health(
    params: dict = Depends(get_engine_health_params),
    logic: EngineHealthLogic = Depends(get_engine_health_logic),
):
    # GET endpoint for engine health data - TODO: test with frontend
    return await logic.engine_health(**params)
