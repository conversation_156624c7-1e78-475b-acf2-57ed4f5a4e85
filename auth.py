from fastapi import Header, Request, HTTPException
import jwt

from app.services.redis_api.redis_api_client import RedisApi


async def managed_owners(x_session_owner_vat: str):
    redis_api = RedisApi()
    owners = await redis_api.get_key_data(
        f"owner_{x_session_owner_vat}:managed_owners"
    )

    owners = owners[0].get("value", [])

    if owners:
        return owners

    raise HTTPException(
        status_code=404,
        detail=f"No managed owners found for X-Tenant-Owner-Vat={x_session_owner_vat}"
    )


async def validate_owner_access(vat: str, x_session_owner_vat: str):
    if not vat or not x_session_owner_vat:
        raise HTTPException(422, "Missing owner_vat or X-Tenant-Owner-Vat")
    managed = await managed_owners(x_session_owner_vat)
    if not any(str(d["vat"]) == str(vat) for d in managed):
        raise HTTPException(403, "Forbidden")


async def owner_vat_check(
    request: Request,
    x_session_owner_vat: str = Header(..., alias="X-Tenant-Owner-Vat"),
) -> str:
    vat = request.query_params.get("owner_vat")
    if vat is None:
        try:
            body = await request.json()
        except Exception:
            raise HTTPException(400, "Invalid JSON body")
        vat = body.get("owner_vat")

    if vat is None:
        raise HTTPException(422, "Missing owner_vat")

    await validate_owner_access(vat, x_session_owner_vat)
    return vat


class AuthHandler:
    def __init__(self, secret_token: str):
        if not secret_token:
            raise ValueError("JWT_SECRET must be set in environment")
        self.secret_token = secret_token

    async def __call__(self, request: Request):
        token = request.headers.get("X-API-Key")
        if not token:
            raise HTTPException(status_code=401, detail="App token is missing")
        try:
            # decode will throw if invalid
            jwt.decode(token, self.secret_token, algorithms=["HS256"])
        except jwt.InvalidTokenError:
            raise HTTPException(status_code=401, detail="Invalid app token")

