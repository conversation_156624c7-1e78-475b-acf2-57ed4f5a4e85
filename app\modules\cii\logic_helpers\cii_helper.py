from fuzzywuzzy import process
import numpy as np


# import matplotlib

# matplotlib.use('Agg')
# import matplotlib.pyplot as plt


class CiiHelper:
    @staticmethod
    def fuel_types_emission_factors():
        return {
            # "MGO": {
            #     "Reference": "ISO 8217 Grades DMX through DMB",
            #     "Lower_calorific_value_kJ_kg": 42700,
            #     "Carbon_content": 0.8744,
            #     "CF_t_CO2_t_Fuel": 3.206,
            # },
            "MDO": {
                "Reference": "ISO 8217 Grades DMX through DMB",
                "Lower_calorific_value_kJ_kg": 42700,
                "Carbon_content": 0.8744,
                "CF_t_CO2_t_Fuel": 3.206,
            },
            "LFO": {
                "Reference": "ISO 8217 Grades RMA through RMD",
                "Lower_calorific_value_kJ_kg": 41200,
                "Carbon_content": 0.8594,
                "CF_t_CO2_t_Fuel": 3.151,
            },
            "HFO": {
                "Reference": "ISO 8217 Grades RME through RMK",
                "Lower_calorific_value_kJ_kg": 40200,
                "Carbon_content": 0.8493,
                "CF_t_CO2_t_Fuel": 3.114,
            },
            # ,
            # "Heavy Fuel Oil (HFO)": {
            #     "Reference": "ISO 8217 Grades RME through RMK",
            #     "Lower_calorific_value_kJ_kg": 40200,
            #     "Carbon_content": 0.8493,
            #     "CF_t_CO2_t_Fuel": 3.114,
            # },
            # "Propane": {
            #     "Lower_calorific_value_kJ_kg": 46300,
            #     "Carbon_content": 0.8182,
            #     "CF_t_CO2_t_Fuel": 3.000,
            # },
            # "Butane": {
            #     "Lower_calorific_value_kJ_kg": 45700,
            #     "Carbon_content": 0.8264,
            #     "CF_t_CO2_t_Fuel": 3.030,
            # },
            # "Ethane": {
            #     "Lower_calorific_value_kJ_kg": 46400,
            #     "Carbon_content": 0.7989,
            #     "CF_t_CO2_t_Fuel": 2.927,
            # },
            # "Liquefied Natural Gas (LNG)": {
            #     "Lower_calorific_value_kJ_kg": 48000,
            #     "Carbon_content": 0.7500,
            #     "CF_t_CO2_t_Fuel": 2.750,
            # },
            # "Methanol": {
            #     "Lower_calorific_value_kJ_kg": 19900,
            #     "Carbon_content": 0.3750,
            #     "CF_t_CO2_t_Fuel": 1.375,
            # },
            # "Ethanol": {
            #     "Lower_calorific_value_kJ_kg": 26800,
            #     "Carbon_content": 0.5217,
            #     "CF_t_CO2_t_Fuel": 1.913,
            # },
        }

    @staticmethod
    def calculate_emissions(fuel_consumption, emission_factor):
        return (fuel_consumption * emission_factor["CF_t_CO2_t_Fuel"]) * 10**6

    @staticmethod
    def get_emissions(fuel_consumption):
        emission_factors = CiiHelper.fuel_types_emission_factors()
        emissions = (
            sum(
                CiiHelper.calculate_emissions(value, emission_factors[key])
                for key, value in fuel_consumption.items()
            )
        ) / 1000000

        return emissions

    @staticmethod
    def calculate_attained_cii(distance, emissions, vessel_capacity):
        attained_cii = None

        if emissions > 0 and distance is not None and distance > 0:
            emissions *= 1000000  # Convert to g
            attained_cii = emissions / (vessel_capacity * distance)

        return attained_cii

    @staticmethod
    def project_cii(
        yearly_overview, vessel_capacity, vessel_type, current_year, cii_ref
    ):
        attained_cii = yearly_overview["cii"]["attained_cii"]
        attained_letter_cii = yearly_overview["cii"]["cii_letter_rating"]
        reduction_factor_years = list(CiiHelper.reduction_factors().keys())
        cii_next_year = None
        deterioration_year = None
        if current_year + 1 in reduction_factor_years:
            # project next years cii
            required_cii = CiiHelper.calculate_required_cii(
                cii_ref, CiiHelper.reduction_factors()[current_year + 1]
            )
            adjusted_boundaries_for_year = CiiHelper.calculate_dd_vector_boundaries(
                required_cii, vessel_capacity, vessel_type
            )
            cii_next_year = CiiHelper.get_letter_cii_rating(
                attained_cii, adjusted_boundaries_for_year
            )

        # find deterioration year
        for year in reduction_factor_years:
            if year > current_year and year <= 2050:
                required_cii = CiiHelper.calculate_required_cii(
                    cii_ref, CiiHelper.reduction_factors()[year]
                )
                adjusted_boundaries_for_year = CiiHelper.calculate_dd_vector_boundaries(
                    required_cii, vessel_capacity, vessel_type
                )
                deterioration_cii_next_year = CiiHelper.get_letter_cii_rating(
                    attained_cii, adjusted_boundaries_for_year
                )
                if ord(attained_letter_cii) < ord(deterioration_cii_next_year):
                    deterioration_year = year
                    break

        return {"next_year": cii_next_year, "deterioration_year": deterioration_year}

    @staticmethod
    def get_letter_cii_rating(cii, boundaries):
        ratings = ["A", "B", "C", "D", "E"]
        for i, boundary in enumerate(boundaries):
            if cii is not None and cii < boundary:
                return ratings[i]
        return ratings[-1]

    @staticmethod
    def reduction_factors():
        start_year = 2023
        end_year = 2050
        start_factor = 5
        increment = 2

        factors = {}
        for year in range(start_year, end_year + 1):
            factors[year] = start_factor
            start_factor += increment

        return factors

    @staticmethod
    def calculate_required_cii(cii_ref, reduction_factor):
        required_cii = (1 - (reduction_factor / 100)) * cii_ref
        return required_cii

    @staticmethod
    def calculate_dd_vector_boundaries(required_cii, vessel_capacity, vessel_type):
        boundaries = [
            round(dd_vector * required_cii, 2)
            for dd_vector in CiiHelper.vessel_dd_vectors(vessel_capacity, vessel_type)
        ]
        return boundaries

    @staticmethod
    def vessel_type_specifics():
        return {
            "Bulk carrier": {
                "capacity": "dead_weight",
                'f"{size} >= 279000"': {"parameters": {"a": 4745, "c": 0.622}},
                'f"{size} < 279000"': {"parameters": {"a": 4745, "c": 0.622}},
            },
            "Gas carrier": {
                "capacity": "dead_weight",
                'f"{size} >= 65000"': {"parameters": {"a": 14405e7, "c": 2.071}},
                'f"{size} < 65000"': {"parameters": {"a": 8104, "c": 0.639}},
            },
            "Tanker": {
                "capacity": "dead_weight",
                "parameters": {"a": 5247, "c": 0.610},
            },
            "Container ship": {
                "capacity": "dead_weight",
                "parameters": {"a": 1984, "c": 0.489},
            },
            "General cargo ship": {
                "capacity": "dead_weight",
                'f"{size} >= 20000"': {"parameters": {"a": 31948, "c": 0.792}},
                'f"{size} < 20000"': {"parameters": {"a": 588, "c": 0.3885}},
            },
            "Refrigerated cargo carrier": {
                "capacity": "dead_weight",
                "parameters": {"a": 4600, "c": 0.557},
            },
            "Combination carrier": {
                "capacity": "dead_weight",
                "parameters": {"a": 5119, "c": 0.622},
            },
            "LNG carrier": {
                "capacity": "dead_weight",
                'f"{size} >= 100000"': {"parameters": {"a": 9.827, "c": 0.000}},
                'f"{size} >= 65000 and {dead_weight} < 100000"': {
                    "parameters": {"a": 14479e10, "c": 2.673}
                },
                'f"{size} < 65000"': {"parameters": {"a": 14779e10, "c": 2.673}},
            },
            "Ro-ro cargo ship (vehicle carrier)": {
                "capacity": "gross_tonnage",
                'f"{size} >= 57700"': {"parameters": {"a": 3627, "c": 0.590}},
                'f"{size} >= 30000 and {gross_tonnage} < 57700"': {
                    "parameters": {"a": 3627, "c": 0.590}
                },
                'f"{size} < 30000"': {"parameters": {"a": 330, "c": 0.329}},
            },
            "Ro-ro cargo ship": {
                "capacity": "gross_tonnage",
                "parameters": {"a": 1967, "c": 0.485},
            },
            "Ro-ro passenger ship": {
                "capacity": "gross_tonnage",
                "parameters": {"a": 2023, "c": 0.460},
            },
            "High-speed craft designed to SOLAS chapter X": {
                "capacity": "gross_tonnage",
                "parameters": {"a": 4196, "c": 0.460},
            },
            "Cruise passenger ship": {
                "capacity": "gross_tonnage",
                "parameters": {"a": 930, "c": 0.383},
            },
        }

    @staticmethod
    def format_vessel_type_from_db(vessel_type_from_db):
        vessel_types = [
            vessel_type for vessel_type in CiiHelper.vessel_type_specifics()
        ]
        return process.extractOne(vessel_type_from_db, vessel_types)[0]

    @staticmethod
    def vessel_dd_vectors(capacity, vessel_type):
        dd_vectors = {
            "Bulk carrier": {"dd_vectors": [0.86, 0.94, 1.06, 1.18]},
            "Gas carrier": {
                'f"{size} >= 65000"': {"dd_vectors": [0.81, 0.91, 1.12, 1.44]},
                'f"{size} < 65000"': {"dd_vectors": [0.85, 0.95, 1.06, 1.25]},
            },
            "Tanker": {"dd_vectors": [0.82, 0.93, 1.08, 1.28]},
            "Container ship": {"dd_vectors": [0.83, 0.94, 1.07, 1.19]},
            "General cargo ship": {"dd_vectors": [0.83, 0.94, 1.06, 1.19]},
            "Refrigerated cargo carrier": {"dd_vectors": [0.78, 0.91, 1.07, 1.20]},
            "Combination carrier": {"dd_vectors": [0.87, 0.96, 1.06, 1.14]},
            "LNG carrier": {
                'f"{size} >= 100000"': {"dd_vectors": [0.89, 0.98, 1.06, 1.13]},
                'f"{size} < 100000"': {"dd_vectors": [0.78, 0.92, 1.10, 1.37]},
            },
            "Ro-ro cargo ship (vehicle carrier)": {
                "dd_vectors": [0.86, 0.94, 1.06, 1.16]
            },
            "Ro-ro cargo ship": {"dd_vectors": [0.76, 0.89, 1.08, 1.27]},
            "Ro-ro passenger ship": {"dd_vectors": [0.76, 0.92, 1.14, 1.30]},
            "Cruise passenger ship": {"dd_vectors": [0.87, 0.95, 1.06, 1.16]},
        }
        dd_vectors_to_return = None
        if len(dd_vectors[vessel_type]) > 1:
            for condition, vectors in dd_vectors[vessel_type].items():
                # because the f-string is inside a string in the dict it is necessary to eval twice
                result = eval(eval(condition.format(size=capacity)))

                if result:
                    dd_vectors_to_return = vectors
        else:
            dd_vectors_to_return = dd_vectors.get(vessel_type).get("dd_vectors")
        return dd_vectors_to_return

    @staticmethod
    async def calculate_cii_slope(data, manual_weekly):
        # Merge the dictionaries: manual_weekly overrides weekly if the key exists
        merged_data = {
            week: manual_weekly[week]
            if manual_weekly is not None and week in manual_weekly
            else data[week]
            for week in data
        }
        cleaned_dict = {k.strip().replace(" '", ""): v for k, v in merged_data.items()}
        ordered_dict = {int(k): cleaned_dict[k] for k in sorted(cleaned_dict, key=int)}

        # Extract the attained_cii_snapshot values and corresponding week keys
        cii_values = [
            d["attained_cii_snapshot"]
            if "attained_cii_snapshot" in d
            else d["cii_snapshot"]
            for d in ordered_dict.values()
        ]

        if len(cii_values) > 0:
            # Filter out None values from cii_values
            cii_values = [value for value in cii_values if value is not None]

            # Generate x_values that match the length of y_values (starting from 0)
            x_values = np.arange(len(cii_values))

            # Convert y_values to floats (ensure CII values are numeric)
            y_values = list(map(float, cii_values))

            # Perform linear regression (fitting a line y = mx + b)
            if len(y_values) > 0 and x_values.size > 1:
                slope, intercept = np.polyfit(x_values, y_values, 1)
                return slope > 0  # Returns True if slope is positive, False if not
            return None
