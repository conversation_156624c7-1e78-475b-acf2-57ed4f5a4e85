# Export Service - handles data access for export operations

from app.services.redis_api.redis_api_client import RedisApi


class ExportService:
    # Service layer for export data operations

    def __init__(self, redis_client: RedisApi = None):
        # Initialize with Redis client (create new one if not provided)
        self.redis = redis_client or RedisApi()

    async def get_cylinder_to(self, request_data):
        # Get cylinder to date from Redis API
        return await self.redis.call(
            path="cylinder_to",
            key="cylinderTo",
            method="POST",
            json=request_data
        )

    async def get_cylinder_from(self, request_data):
        # Get cylinder from date from Redis API
        return await self.redis.call(
            path="cylinder_from",
            key="cylinderFrom",
            method="POST",
            json=request_data
        )

