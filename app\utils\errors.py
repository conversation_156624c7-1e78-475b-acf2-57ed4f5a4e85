import logging
import as<PERSON><PERSON>
import j<PERSON>
from typing import Union
from fastapi import <PERSON>AP<PERSON>, Request, HTTPException
from fastapi.responses import JSONResponse
from fastapi.exceptions import RequestValidationError
# Import Starlette's HTTPException to handle not found errors separately
from starlette.exceptions import HTT<PERSON>Exception as StarletteHTTPException
from httpx import RequestError, TimeoutException, ConnectError
from app.services.redis_api.redis_api_client_error import RedisApiError


def register_error_handlers(app: FastAPI) -> None:
    """
    Register global exception handlers for the FastAPI application.
    
    This function sets up consistent error handling and logging across the application.
    Each handler:
    - Catches a specific type of exception
    - Logs the error with relevant context
    - Returns a standardized JSON response
    
    Args:
        app (FastAPI): The FastAPI application instance
    """

    @app.exception_handler(StarletteHTTPException)
    async def not_found_handler(request: Request, exc: StarletteHTTPException):
        """
        Handle 404 Not Found and other Starlette HTTP exceptions.
        Logs the request method, path and error details.
        """
        logging.warning(
            "404 Not Found: %s %s → %d %s",
            request.method,
            request.url.path,
            exc.status_code,
            exc.detail or "Not Found",
        )
        return J<PERSON>NResponse(
            status_code=exc.status_code,
            content={
                "status": "error",
                "message": exc.detail or "Not Found",
                "data": None,
            },
        )

    @app.exception_handler(HTTPException)
    async def http_exc_handler(request: Request, exc: HTTPException):
        """
        Handle FastAPI's HTTPException.
        Used for explicit error responses raised in route handlers.
        """
        logging.warning(
            "HTTPException: %s %s → %d %s",
            request.method,
            request.url.path,
            exc.status_code,
            exc.detail,
        )
        return JSONResponse(
            status_code=exc.status_code,
            content={
                "status": "error",
                "message": exc.detail,
                "data": None,
            },
        )

    @app.exception_handler(RequestValidationError) 
    async def validation_exc_handler(request: Request, exc: RequestValidationError):
        """
        Handle request validation errors (e.g. invalid data types, missing fields).
        Logs validation errors with detailed error list.
        """
        logging.info(
            "Validation error on %s %s: %s",
            request.method,
            request.url.path,
            exc.errors(),
        )
        return JSONResponse(
            status_code=422,
            content={
                "status": "error",
                "message": "Validation error",
                "errors": exc.errors(), # Include detailed validation errors
                "data": None,
            },
        )

    @app.exception_handler(RedisApiError)
    async def redis_api_error_handler(request: Request, exc: RedisApiError):
        """
        Handle Redis API specific errors.
        These are already HTTPExceptions but we want specific logging for Redis issues.
        """
        logging.error(
            "Redis API error on %s %s: %d %s",
            request.method,
            request.url.path,
            exc.status_code,
            exc.detail,
        )
        return JSONResponse(
            status_code=exc.status_code,
            content={
                "status": "error",
                "message": exc.detail,
                "data": None,
            },
        )

    @app.exception_handler(ConnectError)
    async def connection_error_handler(request: Request, exc: ConnectError):
        """
        Handle network connection errors (Redis, external APIs, etc.).
        """
        logging.error(
            "Connection error on %s %s: %s",
            request.method,
            request.url.path,
            str(exc),
        )
        return JSONResponse(
            status_code=503,  # Service Unavailable
            content={
                "status": "error",
                "message": "Service temporarily unavailable",
                "data": None,
            },
        )

    @app.exception_handler(TimeoutException)
    async def timeout_error_handler(request: Request, exc: TimeoutException):
        """
        Handle request timeout errors.
        """
        logging.warning(
            "Timeout error on %s %s: %s",
            request.method,
            request.url.path,
            str(exc),
        )
        return JSONResponse(
            status_code=504,  # Gateway Timeout
            content={
                "status": "error",
                "message": "Request timeout",
                "data": None,
            },
        )

    @app.exception_handler(asyncio.TimeoutError)
    async def asyncio_timeout_handler(request: Request, exc: asyncio.TimeoutError):
        """
        Handle asyncio timeout errors.
        """
        logging.warning(
            "Asyncio timeout on %s %s: %s",
            request.method,
            request.url.path,
            str(exc),
        )
        return JSONResponse(
            status_code=504,  # Gateway Timeout
            content={
                "status": "error",
                "message": "Operation timeout",
                "data": None,
            },
        )

    @app.exception_handler(json.JSONDecodeError)
    async def json_decode_error_handler(request: Request, exc: json.JSONDecodeError):
        """
        Handle JSON parsing errors.
        """
        logging.warning(
            "JSON decode error on %s %s: %s",
            request.method,
            request.url.path,
            str(exc),
        )
        return JSONResponse(
            status_code=400,  # Bad Request
            content={
                "status": "error",
                "message": "Invalid JSON format",
                "data": None,
            },
        )

    @app.exception_handler(ValueError)
    async def value_error_handler(request: Request, exc: ValueError):
        """
        Handle ValueError exceptions (often from data processing).
        """
        logging.warning(
            "Value error on %s %s: %s",
            request.method,
            request.url.path,
            str(exc),
        )
        return JSONResponse(
            status_code=400,  # Bad Request
            content={
                "status": "error",
                "message": "Invalid data provided",
                "data": None,
            },
        )

    @app.exception_handler(KeyError)
    async def key_error_handler(request: Request, exc: KeyError):
        """
        Handle KeyError exceptions (missing required data).
        """
        logging.warning(
            "Key error on %s %s: Missing key %s",
            request.method,
            request.url.path,
            str(exc),
        )
        return JSONResponse(
            status_code=400,  # Bad Request
            content={
                "status": "error",
                "message": "Missing required data field",
                "data": None,
            },
        )

    @app.exception_handler(Exception)
    async def generic_exc_handler(request: Request, exc: Exception):
        """
        Catch-all handler for any unhandled exceptions.
        Logs full traceback for debugging while returning a generic error to the client.
        """
        logging.exception(
            "Unhandled exception on %s %s: %s",
            request.method,
            request.url.path,
            str(exc),
        )
        return JSONResponse(
            status_code=500,
            content={
                "status": "error",
                "message": "Internal server error", # Generic message for security
                "data": None,
            },
        )
