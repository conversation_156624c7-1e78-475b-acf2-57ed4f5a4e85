# Vessel Logic - handles business logic for vessel management operations

from app.modules.vessel.vessel_service import VesselService
from app.context.utils import select_keys


class VesselLogic:
    # Logic layer for vessel management operations

    def __init__(self, vessel_service: VesselService):
        self.vessel_service = vessel_service

    async def vessels(self, owner_vat: str):
        # Get all vessels for an owner - return as list instead of dict
        vessels = await self.vessel_service.vessels(**select_keys(locals()))
        return list(vessels.values())

