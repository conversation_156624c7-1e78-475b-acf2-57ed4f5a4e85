# Engine Health Service - handles data access for engine health monitoring

from app.services.redis_api.redis_api_client import RedisApi


class EngineHealthService:
    # Service layer for engine health data operations
    # TODO: test with frontend

    def __init__(self, redis_client: RedisApi = None):
        # Initialize with Redis client (create new one if not provided)
        self.redis = redis_client or RedisApi()

    async def engine_health(self, **params):
        # Get engine health data from Redis API
        return await self.redis.call(
            path="engine_health",
            key="enginePerformance",
            **params
        )

