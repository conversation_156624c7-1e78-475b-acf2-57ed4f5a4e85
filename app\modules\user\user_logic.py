# User Logic - handles business logic for user management operations

from app.modules.user.user_service import UserService
from app.context.utils import select_keys


class UserLogic:
    # Logic layer for user management operations

    def __init__(self, user_owner_service: UserService):
        self.user_owner_service = user_owner_service

    async def users_me(self, email: str):
        # Get current user profile by email
        payload = {"email": email}
        return await self.user_owner_service.users_me(
            payload=payload,
        )

    async def users(self, owner_vat: str):
        # Get all users for an owner organization
        return await self.user_owner_service.users(**select_keys(locals()))
