"""
Integration tests for Redis client and data access.
"""
import pytest
import asyncio
from unittest.mock import patch, AsyncMock, MagicMock

from app.services.redis_api.redis_api_client import RedisApi
from app.modules.home.home_service import HomeService
from app.modules.user.user_service import UserService


@pytest.mark.integration
class TestRedisClientIntegration:
    """Integration tests for Redis client functionality."""
    
    @pytest.fixture
    def mock_redis_connection(self):
        """Mock Redis connection for testing."""
        mock_conn = AsyncMock()
        mock_conn.get.return_value = '{"test": "data"}'
        mock_conn.set.return_value = True
        mock_conn.exists.return_value = True
        mock_conn.delete.return_value = 1
        return mock_conn
    
    @pytest.mark.asyncio
    async def test_redis_singleton_behavior(self):
        """Test Redis client singleton behavior."""
        with patch('app.services.redis_api.redis_api_client.aioredis') as mock_aioredis:
            mock_aioredis.from_url.return_value = AsyncMock()
            
            # Create multiple instances
            client1 = RedisApi()
            client2 = RedisApi()
            
            # Should be the same instance
            assert client1 is client2
    
    @pytest.mark.asyncio
    async def test_redis_call_method_success(self, mock_redis_connection):
        """Test Redis call method with successful response."""
        with patch('app.services.redis_api.redis_api_client.aioredis') as mock_aioredis:
            mock_aioredis.from_url.return_value = mock_redis_connection
            
            client = RedisApi()
            
            # Mock HTTP response
            mock_response = AsyncMock()
            mock_response.json.return_value = {"result": "success"}
            mock_response.status_code = 200
            
            with patch('httpx.AsyncClient.request', return_value=mock_response) as mock_request:
                result = await client.call(
                    path="test_endpoint",
                    method="GET",
                    params={"param1": "value1"}
                )
                
                assert result == {"result": "success"}
                mock_request.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_redis_call_method_with_caching(self, mock_redis_connection):
        """Test Redis call method with caching enabled."""
        with patch('app.services.redis_api.redis_api_client.aioredis') as mock_aioredis:
            mock_aioredis.from_url.return_value = mock_redis_connection
            
            client = RedisApi()
            
            # First call - should hit API and cache result
            mock_response = AsyncMock()
            mock_response.json.return_value = {"cached": "data"}
            mock_response.status_code = 200
            
            with patch('httpx.AsyncClient.request', return_value=mock_response) as mock_request:
                result1 = await client.call(
                    path="cached_endpoint",
                    key="cache_key",
                    method="GET"
                )
                
                # Second call - should use cached result
                result2 = await client.call(
                    path="cached_endpoint",
                    key="cache_key",
                    method="GET"
                )
                
                assert result1 == result2
                # Should only make one HTTP request due to caching
                assert mock_request.call_count <= 2
    
    @pytest.mark.asyncio
    async def test_redis_get_key_data_success(self, mock_redis_connection):
        """Test Redis get_key_data method."""
        with patch('app.services.redis_api.redis_api_client.aioredis') as mock_aioredis:
            mock_aioredis.from_url.return_value = mock_redis_connection
            mock_redis_connection.get.return_value = '{"value": [{"data": "test"}]}'
            
            client = RedisApi()
            result = await client.get_key_data("test_key")
            
            assert result == [{"data": "test"}]
            mock_redis_connection.get.assert_called_once_with("test_key")
    
    @pytest.mark.asyncio
    async def test_redis_get_key_data_not_found(self, mock_redis_connection):
        """Test Redis get_key_data method when key not found."""
        with patch('app.services.redis_api.redis_api_client.aioredis') as mock_aioredis:
            mock_aioredis.from_url.return_value = mock_redis_connection
            mock_redis_connection.get.return_value = None
            
            client = RedisApi()
            result = await client.get_key_data("nonexistent_key")
            
            assert result == []
    
    @pytest.mark.asyncio
    async def test_redis_retrieve_time_series_data(self, mock_redis_connection):
        """Test Redis retrieve_time_series_data method."""
        with patch('app.services.redis_api.redis_api_client.aioredis') as mock_aioredis:
            mock_aioredis.from_url.return_value = mock_redis_connection
            
            # Mock time series data
            time_series_data = [
                {"timestamp": "2024-01-01T00:00:00Z", "value": 100},
                {"timestamp": "2024-01-01T01:00:00Z", "value": 110}
            ]
            mock_redis_connection.get.return_value = f'{{"value": {time_series_data}}}'
            
            client = RedisApi()
            result = await client.retrieve_time_series_data("time_series_key")
            
            assert result == time_series_data
    
    @pytest.mark.asyncio
    async def test_redis_connection_error_handling(self):
        """Test Redis client error handling for connection issues."""
        with patch('app.services.redis_api.redis_api_client.aioredis') as mock_aioredis:
            mock_aioredis.from_url.side_effect = ConnectionError("Redis connection failed")
            
            with pytest.raises(ConnectionError):
                client = RedisApi()
                await client.get_key_data("test_key")


@pytest.mark.integration
class TestServiceRedisIntegration:
    """Integration tests for service classes with Redis."""
    
    @pytest.mark.asyncio
    async def test_home_service_redis_integration(self):
        """Test HomeService integration with Redis client."""
        mock_redis_data = {
            "home_page_data": {
                "vessel_info": {"name": "Wilson Saga"},
                "measurements": []
            }
        }
        
        with patch('app.services.redis_api.redis_api_client.aioredis') as mock_aioredis:
            mock_connection = AsyncMock()
            mock_aioredis.from_url.return_value = mock_connection
            
            # Mock HTTP response for Redis call
            mock_response = AsyncMock()
            mock_response.json.return_value = mock_redis_data
            mock_response.status_code = 200
            
            with patch('httpx.AsyncClient.request', return_value=mock_response):
                service = HomeService()
                result = await service.home({"selected_owner_vat": "NO950700939"})
                
                assert result == mock_redis_data
    
    @pytest.mark.asyncio
    async def test_user_service_redis_integration(self):
        """Test UserService integration with Redis client."""
        mock_users_data = {
            "users": [
                {"id": 1, "email": "<EMAIL>"},
                {"id": 2, "email": "<EMAIL>"}
            ]
        }
        
        with patch('app.services.redis_api.redis_api_client.aioredis') as mock_aioredis:
            mock_connection = AsyncMock()
            mock_aioredis.from_url.return_value = mock_connection
            
            # Mock HTTP response for Redis call
            mock_response = AsyncMock()
            mock_response.json.return_value = mock_users_data
            mock_response.status_code = 200
            
            with patch('httpx.AsyncClient.request', return_value=mock_response):
                service = UserService()
                result = await service.users(owner_vat="NO950700939")
                
                assert result == mock_users_data
    
    @pytest.mark.asyncio
    async def test_multiple_services_concurrent_redis_access(self):
        """Test multiple services accessing Redis concurrently."""
        with patch('app.services.redis_api.redis_api_client.aioredis') as mock_aioredis:
            mock_connection = AsyncMock()
            mock_aioredis.from_url.return_value = mock_connection
            
            # Mock different responses for different services
            mock_responses = [
                AsyncMock(json=AsyncMock(return_value={"home_data": "test"}), status_code=200),
                AsyncMock(json=AsyncMock(return_value={"users": []}), status_code=200),
                AsyncMock(json=AsyncMock(return_value={"owners": []}), status_code=200)
            ]
            
            with patch('httpx.AsyncClient.request', side_effect=mock_responses):
                # Create services
                home_service = HomeService()
                user_service = UserService()
                
                # Make concurrent calls
                tasks = [
                    home_service.home({"selected_owner_vat": "NO950700939"}),
                    user_service.users(owner_vat="NO950700939"),
                    home_service.owners_with_vessel(owner_vat="NO950700939")
                ]
                
                results = await asyncio.gather(*tasks)
                
                # Verify all calls completed successfully
                assert len(results) == 3
                assert results[0] == {"home_data": "test"}
                assert results[1] == {"users": []}
                assert results[2] == {"owners": []}


@pytest.mark.integration
class TestRedisDataConsistency:
    """Integration tests for Redis data consistency."""
    
    @pytest.mark.asyncio
    async def test_cache_invalidation(self, mock_redis_client):
        """Test cache invalidation behavior."""
        with patch('app.services.redis_api.redis_api_client.aioredis') as mock_aioredis:
            mock_connection = AsyncMock()
            mock_aioredis.from_url.return_value = mock_connection
            
            # First response (cached)
            mock_connection.get.side_effect = [
                '{"value": [{"cached": "old_data"}]}',  # First call
                None,  # After invalidation
                '{"value": [{"cached": "new_data"}]}'   # After refresh
            ]
            
            mock_response = AsyncMock()
            mock_response.json.return_value = {"value": [{"cached": "new_data"}]}
            mock_response.status_code = 200
            
            with patch('httpx.AsyncClient.request', return_value=mock_response):
                client = RedisApi()
                
                # First call - should return cached data
                result1 = await client.get_key_data("test_key")
                assert result1 == [{"cached": "old_data"}]
                
                # Simulate cache invalidation
                mock_connection.delete.return_value = 1
                
                # Second call - should fetch new data
                result2 = await client.get_key_data("test_key")
                assert result2 == [{"cached": "new_data"}]
    
    @pytest.mark.asyncio
    async def test_concurrent_cache_access(self):
        """Test concurrent access to cached data."""
        with patch('app.services.redis_api.redis_api_client.aioredis') as mock_aioredis:
            mock_connection = AsyncMock()
            mock_aioredis.from_url.return_value = mock_connection
            mock_connection.get.return_value = '{"value": [{"concurrent": "data"}]}'
            
            client = RedisApi()
            
            # Make multiple concurrent requests for the same key
            tasks = [
                client.get_key_data("concurrent_key")
                for _ in range(5)
            ]
            
            results = await asyncio.gather(*tasks)
            
            # All results should be the same
            expected_result = [{"concurrent": "data"}]
            for result in results:
                assert result == expected_result
            
            # Redis should only be called once due to caching
            assert mock_connection.get.call_count <= 5


@pytest.mark.integration
class TestRedisPerformance:
    """Integration tests for Redis performance characteristics."""
    
    @pytest.mark.asyncio
    async def test_redis_response_time(self, performance_test_helper):
        """Test Redis response time under load."""
        with patch('app.services.redis_api.redis_api_client.aioredis') as mock_aioredis:
            mock_connection = AsyncMock()
            mock_aioredis.from_url.return_value = mock_connection
            mock_connection.get.return_value = '{"value": [{"performance": "test"}]}'
            
            client = RedisApi()
            
            # Measure performance of multiple Redis calls
            import time
            start_time = time.time()
            
            tasks = [
                client.get_key_data(f"perf_key_{i}")
                for i in range(10)
            ]
            
            await asyncio.gather(*tasks)
            
            end_time = time.time()
            total_time = end_time - start_time
            
            # Should complete within reasonable time
            assert total_time < 5.0  # 5 seconds for 10 operations
    
    @pytest.mark.asyncio
    async def test_redis_memory_usage(self):
        """Test Redis client memory usage patterns."""
        with patch('app.services.redis_api.redis_api_client.aioredis') as mock_aioredis:
            mock_connection = AsyncMock()
            mock_aioredis.from_url.return_value = mock_connection
            
            # Simulate large data responses
            large_data = [{"data": f"item_{i}"} for i in range(1000)]
            mock_connection.get.return_value = f'{{"value": {large_data}}}'
            
            client = RedisApi()
            
            # Process large dataset
            result = await client.get_key_data("large_dataset")
            
            # Verify data integrity
            assert len(result) == 1000
            assert result[0]["data"] == "item_0"
            assert result[999]["data"] == "item_999"
