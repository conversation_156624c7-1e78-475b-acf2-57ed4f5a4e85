# Anomaly Detection Service - handles data access for anomaly detection

from app.services.redis_api.redis_api_client import RedisApi


class AnomalyDetectionService:
    # Service layer for anomaly detection data operations

    def __init__(self, redis_client: RedisApi = None):
        # Initialize with Redis client (create new one if not provided)
        self.redis = redis_client or RedisApi()

    async def anomaly_detection(self, payload):
        # Call Redis API to get anomaly detection data
        return await self.redis.call(
            path="anomaly_detection",
            key="anomaly_detection_data",
            method="POST",
            json=payload,
        )

