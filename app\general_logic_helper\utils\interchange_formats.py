import logging
import math
from enum import Enum

from app.general_logic_helper.utils.special_vessel_keys import aux_do_vessel_id_key_relation

from orjson import orjson

_mysql_date_format = "%Y-%m-%d %H:%M:%S"
_transmit_dict = {"timestamp": "NULL", "messages": [], "measurements": []}

# DO NOT CHANGE without thinking a lot first.
# Used by Excel Helper and Bi Connector Task.
_interchange_dict_v2 = {
    "timestamp": "NULL",
    "draft_fwd": "NULL",
    "draft_aft": "NULL",
    "log_speed": "NULL",
    "gps_speed": "NULL",
    "fuel_temperature": "NULL",
    # 'fuel_type': 'NULL',
    "latitude": "NULL",
    "longitude": "NULL",
    "heading": "NULL",
    "shaft_1_rpm": "NULL",
    "shaft_1_torque": "NULL",
    "shaft_1_power": "NULL",
    "shaft_1_thrust": "NULL",
    # 'prop_1_pitch': 'NULL',
    "me_1_load": "NULL",
    "me_1_rpm": "NULL",
    "fuel_density": "NULL",
    "me_fuel_total": "NULL",
    "me_1_fc_mass": "NULL",
    # 'me_1_fc_volume': 'NULL',
    "aux_net_fc_mass": "NULL",
    "aux_1_fc_mass": "NULL",
    "aux_2_fc_mass": "NULL",
    # 'aux_1_fc_volume': 'NULL',
    # 'aux_2_fc_volume': 'NULL',
    # 'boiler_net_fc_mass': 'NULL',
    "boiler_1_fc_mass": "NULL",
    "boiler_2_fc_mass": "NULL",
    # 'boiler_1_fc_volume': 'NULL',
    # 'boiler_2_fc_volume': 'NULL',
    "flow_totals": "NULL",
    # 'gyro_x': 'NULL',
    # 'gyro_y': 'NULL',
    # 'gyro_z': 'NULL',
    # 'accel_x': 'NULL',
    # 'accel_y': 'NULL',
    # 'accel_z': 'NULL',
    "frugal_status": "NULL",
    "frugal_mode": "NULL",
    "sailing_status": "NULL",
    # 'frugal_alarms': 'NULL',
    # 'thrust_cmd': 'NULL',
    # 'rpm_cmd': 'NULL',
    # 'pitch_cmd': 'NULL',
    # 'state_regs': 'NULL',
    "version": 2.0,
}

# Used By Redis Task
_sparse_dict_v2 = {
    "timestamp": "NULL",
    "df": "NULL",
    "da": "NULL",
    "lspd": "NULL",
    "gspd": "NULL",
    "fdens": "NULL",
    # 'ftemp': 'NULL',
    # 'ftype': 'NULL',
    "lat": "NULL",
    "lon": "NULL",
    "hdg": "NULL",
    "srpm": "NULL",
    "strq": "NULL",
    "spow": "NULL",
    # 'ptch': 'NULL',
    "mefcm": "NULL",
    # 'mefcv': 'NULL',
    "meld": "NULL",
    "merpm": "NULL",
    "auxfcm": "NULL",
    # 'eulx': 'NULL',
    # 'euly': 'NULL',
    # 'eulz': 'NULL',
    # 'accx': 'NULL',
    # 'accy': 'NULL',
    # 'accz': 'NULL',
    "fstat": "NULL",
    "fmode": "NULL",
    "b1fcm": "NULL",
    "b2fcm": "NULL",
    # 'b1fcv': 'NULL',
    # 'b2fcv': 'NULL',
    "flwt": "NULL",
    "nstat": "NULL",
    "sstat": "NULL",
    # 'falm': 'NULL',
    # 'thrcmd': 'NULL',
    # 'rpmcmd': 'NULL',
    # 'ptchcmd': 'NULL',
    "ver": 2.0,
}

# DO NOT CHANGE without thinking a lot first.
# Used by Chunk Loader Task
_db_dict_v2 = {
    "vessel": "NULL",
    "timestamp": "NULL",
    "draft_fwd": "NULL",
    "draft_aft": "NULL",
    "log_speed": "NULL",
    "gps_speed": "NULL",
    "fuel_density": "NULL",
    "fuel_temperature": "NULL",
    # 'fuel_type': 'NULL',
    "latitude": "NULL",
    "longitude": "NULL",
    "heading": "NULL",
    "shaft_1_rpm": "NULL",
    "shaft_1_torque": "NULL",
    "shaft_1_thrust": "NULL",
    "shaft_1_power": "NULL",
    "prop_1_pitch": "NULL",
    "me_1_fc_mass": "NULL",
    # 'me_1_fc_volume': 'NULL',
    "me_1_load": "NULL",
    "me_1_rpm": "NULL",
    # 'aux_1_fc_volume': 'NULL',
    # 'aux_2_fc_volume': 'NULL',
    "aux_1_fc_mass": "NULL",
    "aux_2_fc_mass": "NULL",
    # 'boiler_1_fc_volume': 'NULL',
    # 'boiler_2_fc_volume': 'NULL',
    "boiler_1_fc_mass": "NULL",
    "boiler_2_fc_mass": "NULL",
    "flow_totals": "NULL",
    # 'gyro_x': 'NULL',
    # 'gyro_y': 'NULL',
    # 'gyro_z': 'NULL',
    # 'accel_x': 'NULL',
    # 'accel_y': 'NULL',
    # 'accel_z': 'NULL',
    "frugal_status": "NULL",
    "frugal_mode": "NULL",
    # 'frugal_alarms': 'NULL',
    # 'thrust_cmd': 'NULL',
    # 'rpm_cmd': 'NULL',
    # 'pitch_cmd': 'NULL',
    # 'state_regs': 'NULL',
}


class InterchangeFormats:
    class LoadCurveType(Enum):
        LOAD = 0
        PITCH = 1
        POWER = 2

    class SpeedLogType(Enum):
        RELIABLE = 0
        UNRELIABLE = 1
        BROKEN = 2

    class FuelType(Enum):
        MDO = 0
        MGO = 1
        VLSFO = 2
        ULSFO = 3
        HFO = 4

    class FlowSensorType(Enum):
        ACCURATE = 0
        NOISY = 1

    BASIC_CONFIG = {
        "curve_serial": 0,  # Currently deployed prop curve
        "min_draft": 0.0,  # Minimum draft for sizing of intervals
        "max_draft": 100.0,  # Max draft for sizing
        "draft_intervals": 1,  # Number of draft intervals to generate curves for
        "min_me_rpm": 0.0,  # Min main engine RPM - measured by PCS
        "max_me_rpm": 1000.0,  # Max main engine RPM
        "min_shaft_rpm": 0.0,  # Min shaft RPM - measured by torque sensor
        "max_shaft_rpm": 200.0,  # Max shaft RPM
        "max_engine_power": 20000.0,  # Rated engine power (by manufacturer)
        "gear_ratio": 1.0,  # Gear ratio from manufacturer (converts betw. ME RPM and shaft RPM)
        "min_pitch": 0.0,  # Min allowable pitch value
        "max_pitch": 100.0,  # Max allowable pitch value
        "pitch_hysteresis": 2.0,  # Max allowable difference between pitch command and feedback
        "min_me_fc": 0.0,  # Min main engine FC to consider interesting
        "max_me_fc": 100000.0,  # Max main engine FC allowed before considering it an error
        "load_curve_specifies": LoadCurveType.LOAD.name,  # Is the load curve limiting load, power or pitch?
        "load_curve": "[[], []]",
        # Load curve where load_curve[0] is an RPM list and load_curve[1] is load, pitch or pwr
        "speed_log_type": SpeedLogType.RELIABLE.name,  # What can we expect of the speed log - good or bad?
        "flow_sensor_type": FlowSensorType.ACCURATE.name,  # Is the flow sensing accurate or no on this vessel?
        "min_log_speed": 0.0,  # Minimum log speed to consider
        "max_log_speed": 100.0,  # Maximum log speed that is not an error
        "min_gps_speed": 0.0,  # Minimum GPS speed to consider
        "max_gps_speed": 100.0,  # Maximum GPS speed that is not an error
        "min_shaft_power": 0.0,  # Minimum shaft power of interest
        "max_shaft_power": 100000.0,  # Maximum shaft power that is not an error
        "min_shaft_torque": 0.0,  # Minimum shaft torque
        "max_shaft_torque": 1000.0,  # Maximum shaft torque that is not an error
        "max_tci_temp": 550.0,  # Maximum allowable turbocharger inlet temperature
        "max_euler_sum": 1.0,  # Limit on the sum of Euler angle movement (noise filter)
        "max_acceleration": 0.1,  # Limit on acceleration (i.e. waves, swells - noise filter)
        "sample_window_days": 30.0,  # Number of days in the calculation window
        "dead_weight": 0.0,  # Vessel dead weight - i.e. maximum cargo weight
        "gross_tonnage": 0.0,  # Vessel gross tonnage - i.e. weight with no cargo
        "cii_r_factor": 1.0,  # Needed for the CII corrective fc factor that (chemical) tankers need
        "fuel_type": FuelType.VLSFO.name,
        # Standard fuel type for this vessel - does _not_ account for any switching that might happen.
    }

    def __init__(self):
        print("No need to instantiate.")

    @staticmethod
    def clone_transmit_dict():
        return _transmit_dict.copy()

    @staticmethod
    # Used by Excel Helper.
    def clone_interchange_dict_v2():
        return _interchange_dict_v2.copy()

    @staticmethod
    # Used by Chunk Loader Task
    def clone_db_dict_v2():
        return _db_dict_v2.copy()

    @staticmethod
    # Used by Redis Task
    def clone_sparse_dict_v2():
        return _sparse_dict_v2.copy()

    @staticmethod
    # Used by Redis Task.
    # Also used by My Data BE - Confirmed 15/03/2025
    def map_db_to_sparse(db_dict: dict, vessel_id=None) -> dict:
        if db_dict is None:
            raise ValueError("No DB dict supplied - cowardly refusing to continue.")
        spd = InterchangeFormats.clone_sparse_dict_v2()
        spd["timestamp"] = db_dict.get("timestamp", "NULL")
        spd["df"] = db_dict.get("draft_fwd", "NULL")
        spd["da"] = db_dict.get("draft_aft", "NULL")
        spd["lspd"] = db_dict.get("log_speed", "NULL")
        spd["gspd"] = db_dict.get("gps_speed", "NULL")
        spd["fdens"] = db_dict.get("fuel_density", "NULL")
        # spd['ftemp'] = db_dict.get('fuel_temperature', 'NULL')
        # spd['ftype'] = db_dict.get('fuel_type', 'NULL')
        spd["lat"] = db_dict.get("latitude", "NULL")
        spd["lon"] = db_dict.get("longitude", "NULL")
        spd["hdg"] = db_dict.get("heading", "NULL")
        spd["srpm"] = db_dict.get("shaft_1_rpm", "NULL")
        spd["strq"] = db_dict.get("shaft_1_torque", "NULL")
        spd["spow"] = db_dict.get("shaft_1_power", "NULL")
        # spd['ptch'] = db_dict.get('prop_1_pitch', 'NULL')
        spd["mefcm"] = db_dict.get("me_1_fc_mass", "NULL")
        # spd['mefcv'] = db_dict.get('me_1_fc_volume', 'NULL')
        spd["meld"] = db_dict.get("me_1_load", "NULL")
        spd["merpm"] = db_dict.get("me_1_rpm", "NULL")
        spd["auxfcm"] = db_dict.get("aux_net_fc_mass", "NULL")
        # spd['auxfcv1'] = db_dict.get('aux_1_fc_volume', 'NULL')
        # spd['auxfcv2'] = db_dict.get('aux_2_fc_volume', 'NULL')
        # spd['eulx'] = db_dict.get('gyro_x', 'NULL')
        # spd['euly'] = db_dict.get('gyro_y', 'NULL')
        # spd['eulz'] = db_dict.get('gyro_z', 'NULL')
        # spd['accx'] = db_dict.get('accel_x', 'NULL')
        # spd['accy'] = db_dict.get('accel_y', 'NULL')
        # spd['accz'] = db_dict.get('accel_z', 'NULL')
        spd["fstat"] = db_dict.get("frugal_status", "NULL")
        frugal_mode = db_dict.get("frugal_mode", "NULL")
        if (
            frugal_mode is not None
            and frugal_mode != "NULL"
            and not math.isnan(frugal_mode)
        ):
            spd["fmode"] = int(frugal_mode)
        spd["boifcm"] = db_dict.get("boiler_net_fc_mass", "NULL")
        spd["b1fcm"] = db_dict.get("boiler_1_fc_mass", "NULL")
        spd["b2fcm"] = db_dict.get("boiler_2_fc_mass", "NULL")
        # spd['b1fcv'] = db_dict.get('boiler_1_fc_volume', 'NULL')
        # spd['b2fcv'] = db_dict.get('boiler_2_fc_volume', 'NULL')
        spd["flwt"] = db_dict.get("flow_totals", "NULL")
        spd["nstat"] = db_dict.get("nav_status", "NULL")
        spd["sstat"] = db_dict.get("sailing_status", "NULL")
        # spd['falm'] = db_dict.get('frugal_alarms', 'NULL')
        # spd['thrcmd'] = db_dict.get('thrust_cmd', 'NULL')
        # spd['rpmcmd'] = db_dict.get('rpm_cmd', 'NULL')
        # spd['ptchcmd'] = db_dict.get('pitch_cmd', 'NULL')
        if vessel_id != None:
            # Pull out diesel data and make new column
            if vessel_id in aux_do_vessel_id_key_relation.keys():
                consumption = db_dict.get("consumption", "NULL")
                aux_do_is_none = (
                    consumption == "NULL"
                    or consumption is None
                    or (spd["auxfcm"] is not None and spd["auxfcm"] >= 5)
                )
                if isinstance(consumption, str):
                    consumption = orjson.loads(consumption)
                spd["auxdo"] = (
                    0
                    if aux_do_is_none
                    else consumption[aux_do_vessel_id_key_relation[vessel_id]]
                )

        return spd

    @staticmethod
    def _add_generated_values(values: dict, aux_add_groups=None, me_fc_add_groups=None):
        # Make any values that need to be calculated from raw measurements
        try:  # Make the net aux flow.
            has_anomalies = values.pop("has_anomalies", None)
            flow_a = values.get("aux_1_fc_mass", None)
            flow_b = values.get("aux_2_fc_mass", None)
            if flow_a is not None and flow_b is not None:
                if aux_add_groups is True:
                    net_flow = abs(float(flow_a) + float(flow_b))
                else:
                    net_flow = abs(float(flow_a) - float(flow_b))
                if net_flow < 0:
                    net_flow = 0
                values["aux_net_fc_mass"] = net_flow
            else:
                values["aux_net_fc_mass"] = "NULL"
            values["has_anomalies"] = has_anomalies
        except Exception as e:
            logging.error("Error while parsing AUX flow data: %s", e)
        try:  # Make the net ME fc flow.
            has_anomalies = values.pop("has_anomalies", None)
            flow_a = values.get("me_1_fc_mass", None)
            flow_b = values.get("me_2_fc_mass", None)
            if flow_a is not None and flow_b is not None:
                if me_fc_add_groups is True:
                    net_flow = abs(float(flow_a) + float(flow_b))
                else:
                    net_flow = abs(float(flow_a) - float(flow_b))
                if net_flow < 0:
                    net_flow = 0
                values["me_net_fc_mass"] = net_flow
            else:
                values["me_net_fc_mass"] = "NULL"
            values["has_anomalies"] = has_anomalies
        except Exception as e:
            logging.error("Error while parsing ME fc flow data: %s", e)

    @staticmethod
    # Used by Redis Task and My Data backend
    def convert_to_ic_format(db_dicts: list, vessel_id=None):
        ic_dicts = []
        if db_dicts is not None:
            for db_dict in db_dicts:
                if db_dict is not None:
                    InterchangeFormats._add_generated_values(db_dict)
                    ic_dict = InterchangeFormats.map_db_to_sparse(
                        db_dict, vessel_id=vessel_id
                    )
                    ic_dicts.append(ic_dict)
            if len(db_dicts) > len(ic_dicts):
                logging.warning("There are fewer IC dicts that DB dicts! BAD!")
        else:
            logging.warning("Collection of DB dicts is None")
        return ic_dicts

    @staticmethod
    # Used by Excel Helper From Database
    def calculate_fuel_flow(db_dicts: list):
        ic_dicts = []
        if db_dicts is not None:
            for db_dict in db_dicts:
                if db_dict is not None:
                    InterchangeFormats._add_generated_values(db_dict)
                    ic_dicts.append(db_dict)
            if len(db_dicts) > len(ic_dicts):
                logging.warning("There are fewer IC dicts that DB dicts! BAD!")
        else:
            logging.warning("Collection of DB dicts is None")
        return ic_dicts
