"""
Integration tests for API endpoints.
"""
import pytest
import json
from unittest.mock import patch, AsyncMock
from fastapi import status
from httpx import AsyncClient

from test.utils.test_helpers import APITestHelper


@pytest.mark.integration
class TestHomeEndpoints:
    """Integration tests for Home module endpoints."""
    
    @pytest.mark.asyncio
    async def test_home_endpoint_success(self, async_client: AsyncClient, auth_headers, mock_auth_dependencies):
        """Test home endpoint with successful response."""
        # Mock Redis response
        mock_redis_data = {
            "home_page_data": {
                "vessel_info": {"name": "Wilson Saga", "imo": "8918461"},
                "measurements": [{"timestamp": "2024-01-01T00:00:00Z", "value": 100}],
                "summary": {"total_fuel": 1000, "avg_speed": 12.5}
            }
        }
        
        with patch('app.services.redis_api.redis_api_client.RedisApi') as mock_redis:
            mock_client = AsyncMock()
            mock_redis.return_value = mock_client
            mock_client.call.return_value = mock_redis_data
            mock_client.get_key_data.return_value = [{"value": []}]
            
            # Test GET request
            response = await async_client.get(
                "/home?owner_vat=NO950700939&vessel_imo=8918461&vessel_id=32&vessel_name=Wilson Saga&from_date=2024-01-01 00:00:00&to_date=2024-01-07 23:59:59",
                headers=auth_headers
            )
            
            assert response.status_code == status.HTTP_200_OK
            data = response.json()
            assert "vessel_info" in data or "home_page_data" in data
    
    @pytest.mark.asyncio
    async def test_home_endpoint_missing_auth(self, async_client: AsyncClient):
        """Test home endpoint without authentication."""
        response = await async_client.get(
            "/home?owner_vat=NO950700939&vessel_imo=8918461"
        )
        
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
    
    @pytest.mark.asyncio
    async def test_home_endpoint_missing_owner_vat(self, async_client: AsyncClient, auth_headers):
        """Test home endpoint without owner_vat parameter."""
        response = await async_client.get(
            "/home?vessel_imo=8918461",
            headers=auth_headers
        )
        
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
    
    @pytest.mark.asyncio
    async def test_home_endpoint_invalid_owner_vat(self, async_client: AsyncClient, auth_headers):
        """Test home endpoint with invalid owner_vat."""
        with patch('auth.validate_owner_access') as mock_validate:
            from fastapi import HTTPException
            mock_validate.side_effect = HTTPException(status_code=403, detail="Forbidden")
            
            response = await async_client.get(
                "/home?owner_vat=INVALID123&vessel_imo=8918461",
                headers=auth_headers
            )
            
            assert response.status_code == status.HTTP_403_FORBIDDEN


@pytest.mark.integration
class TestUserEndpoints:
    """Integration tests for User module endpoints."""
    
    @pytest.mark.asyncio
    async def test_users_endpoint_success(self, async_client: AsyncClient, auth_headers, mock_auth_dependencies):
        """Test users endpoint with successful response."""
        mock_users_data = {
            "users": [
                {"id": 1, "email": "<EMAIL>", "name": "User One"},
                {"id": 2, "email": "<EMAIL>", "name": "User Two"}
            ]
        }
        
        with patch('app.services.redis_api.redis_api_client.RedisApi') as mock_redis:
            mock_client = AsyncMock()
            mock_redis.return_value = mock_client
            mock_client.call.return_value = mock_users_data
            
            response = await async_client.get(
                "/users?owner_vat=NO950700939",
                headers=auth_headers
            )
            
            assert response.status_code == status.HTTP_200_OK
            data = response.json()
            assert "users" in data or isinstance(data, list)
    
    @pytest.mark.asyncio
    async def test_users_endpoint_empty_result(self, async_client: AsyncClient, auth_headers, mock_auth_dependencies):
        """Test users endpoint with empty result."""
        with patch('app.services.redis_api.redis_api_client.RedisApi') as mock_redis:
            mock_client = AsyncMock()
            mock_redis.return_value = mock_client
            mock_client.call.return_value = {"users": []}
            
            response = await async_client.get(
                "/users?owner_vat=NO950700939",
                headers=auth_headers
            )
            
            assert response.status_code == status.HTTP_200_OK


@pytest.mark.integration
class TestOwnerEndpoints:
    """Integration tests for Owner module endpoints."""
    
    @pytest.mark.asyncio
    async def test_owners_endpoint_success(self, async_client: AsyncClient, auth_headers, mock_auth_dependencies):
        """Test owners endpoint with successful response."""
        mock_owners_data = {
            "owners": [
                {"vat": "NO950700939", "name": "Test Owner", "vessels_count": 5}
            ]
        }
        
        with patch('app.services.redis_api.redis_api_client.RedisApi') as mock_redis:
            mock_client = AsyncMock()
            mock_redis.return_value = mock_client
            mock_client.call.return_value = mock_owners_data
            
            response = await async_client.get(
                "/owners?session_owner_vat=NO950700939",
                headers=auth_headers
            )
            
            assert response.status_code == status.HTTP_200_OK
            data = response.json()
            assert "owners" in data or isinstance(data, list)


@pytest.mark.integration
class TestVesselEndpoints:
    """Integration tests for Vessel module endpoints."""
    
    @pytest.mark.asyncio
    async def test_vessels_endpoint_success(self, async_client: AsyncClient, auth_headers, mock_auth_dependencies):
        """Test vessels endpoint with successful response."""
        mock_vessels_data = {
            "vessels": [
                {"imo": "8918461", "name": "Wilson Saga", "type": "General Cargo"}
            ]
        }
        
        with patch('app.services.redis_api.redis_api_client.RedisApi') as mock_redis:
            mock_client = AsyncMock()
            mock_redis.return_value = mock_client
            mock_client.call.return_value = mock_vessels_data
            
            response = await async_client.get(
                "/vessels?owner_vat=NO950700939",
                headers=auth_headers
            )
            
            assert response.status_code == status.HTTP_200_OK


@pytest.mark.integration
class TestDataAnalyticsEndpoints:
    """Integration tests for Data Analytics module endpoints."""
    
    @pytest.mark.asyncio
    async def test_data_analytics_endpoint_success(self, async_client: AsyncClient, auth_headers, mock_auth_dependencies):
        """Test data analytics endpoint with successful response."""
        mock_analytics_data = {
            "analytics": {
                "fuel_consumption": {"total": 1000, "average": 50},
                "efficiency": {"score": 85, "trend": "improving"}
            }
        }
        
        with patch('app.services.redis_api.redis_api_client.RedisApi') as mock_redis:
            mock_client = AsyncMock()
            mock_redis.return_value = mock_client
            mock_client.call.return_value = mock_analytics_data
            mock_client.get_key_data.return_value = [{"value": True}]  # log_speed_reliable
            
            payload = {
                "owner_vat": "NO950700939",
                "vessel_imo": "8918461",
                "vessel_id": 32,
                "vessel_name": "Wilson Saga",
                "from_date": "2024-01-01 00:00:00",
                "to_date": "2024-01-07 23:59:59"
            }
            
            response = await async_client.post(
                "/data-analytics",
                json=payload,
                headers=auth_headers
            )
            
            assert response.status_code == status.HTTP_200_OK
    
    @pytest.mark.asyncio
    async def test_data_analytics_endpoint_invalid_payload(self, async_client: AsyncClient, auth_headers):
        """Test data analytics endpoint with invalid payload."""
        invalid_payload = {
            "invalid_field": "invalid_value"
        }
        
        response = await async_client.post(
            "/data-analytics",
            json=invalid_payload,
            headers=auth_headers
        )
        
        # Should return error due to missing required fields
        assert response.status_code in [status.HTTP_422_UNPROCESSABLE_ENTITY, status.HTTP_400_BAD_REQUEST]


@pytest.mark.integration
class TestAsyncTestEndpoints:
    """Integration tests for Async Test module endpoints."""
    
    @pytest.mark.asyncio
    async def test_quick_response_endpoint(self, async_client: AsyncClient):
        """Test quick response endpoint."""
        response = await async_client.get("/async-test/quick")
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert "message" in data
        assert data["message"] == "Quick response"
        assert "random_number" in data
        assert "timestamp" in data
    
    @pytest.mark.asyncio
    async def test_health_endpoint(self, async_client: AsyncClient):
        """Test health check endpoint."""
        response = await async_client.get("/async-test/health")
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert "status" in data
        assert data["status"] == "healthy"


@pytest.mark.integration
class TestErrorHandling:
    """Integration tests for error handling across endpoints."""
    
    @pytest.mark.asyncio
    async def test_redis_connection_error(self, async_client: AsyncClient, auth_headers, mock_auth_dependencies):
        """Test endpoint behavior when Redis connection fails."""
        with patch('app.services.redis_api.redis_api_client.RedisApi') as mock_redis:
            mock_client = AsyncMock()
            mock_redis.return_value = mock_client
            mock_client.call.side_effect = ConnectionError("Redis connection failed")
            
            response = await async_client.get(
                "/users?owner_vat=NO950700939",
                headers=auth_headers
            )
            
            # Should return 500 Internal Server Error
            assert response.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR
    
    @pytest.mark.asyncio
    async def test_invalid_json_payload(self, async_client: AsyncClient, auth_headers):
        """Test endpoint behavior with invalid JSON payload."""
        response = await async_client.post(
            "/data-analytics",
            content="invalid json content",
            headers={**auth_headers, "Content-Type": "application/json"}
        )
        
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
    
    @pytest.mark.asyncio
    async def test_missing_content_type(self, async_client: AsyncClient, auth_headers):
        """Test endpoint behavior with missing content type."""
        headers = {k: v for k, v in auth_headers.items() if k != "Content-Type"}
        
        response = await async_client.post(
            "/data-analytics",
            json={"owner_vat": "NO950700939"},
            headers=headers
        )
        
        # FastAPI should handle this gracefully
        assert response.status_code in [status.HTTP_200_OK, status.HTTP_422_UNPROCESSABLE_ENTITY]


@pytest.mark.integration
class TestConcurrentRequests:
    """Integration tests for concurrent request handling."""
    
    @pytest.mark.asyncio
    async def test_concurrent_quick_requests(self, async_client: AsyncClient, performance_test_helper):
        """Test multiple concurrent quick requests."""
        results = await performance_test_helper.measure_concurrent_requests(
            async_client,
            "/async-test/quick",
            num_requests=10
        )
        
        # All requests should succeed
        assert results["success_rate"] >= 0.9
        assert results["avg_response_time"] < 2.0  # Should be very fast
    
    @pytest.mark.asyncio
    async def test_mixed_endpoint_concurrency(self, async_client: AsyncClient, auth_headers, mock_auth_dependencies):
        """Test concurrent requests to different endpoints."""
        import asyncio
        
        with patch('app.services.redis_api.redis_api_client.RedisApi') as mock_redis:
            mock_client = AsyncMock()
            mock_redis.return_value = mock_client
            mock_client.call.return_value = {"data": "test"}
            mock_client.get_key_data.return_value = [{"value": []}]
            
            # Create concurrent requests to different endpoints
            tasks = [
                async_client.get("/async-test/quick"),
                async_client.get("/users?owner_vat=NO950700939", headers=auth_headers),
                async_client.get("/owners?session_owner_vat=NO950700939", headers=auth_headers),
                async_client.get("/vessels?owner_vat=NO950700939", headers=auth_headers),
            ]
            
            responses = await asyncio.gather(*tasks, return_exceptions=True)
            
            # Check that all requests completed
            assert len(responses) == 4
            
            # Check that most requests succeeded
            successful_responses = [r for r in responses if not isinstance(r, Exception) and hasattr(r, 'status_code') and r.status_code < 400]
            assert len(successful_responses) >= 3  # At least 3 out of 4 should succeed
