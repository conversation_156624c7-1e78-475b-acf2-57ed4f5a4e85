import inspect
from typing import Dict, Any, List, Optional, Iterable, Union
from fastapi import Request
from fastapi.params import Body as BodyParam


async def _gather_and_cast_params(
    request: Request,
    sig: inspect.Signature,
    *,
    include_body: bool = False,
    headers_to_include: Optional[Union[List[str], Dict[str, str]]] = None,
) -> Dict[str, Any]:
    """
    Gathers and type-casts parameters from an HTTP request based on a function's signature.
    
    Args:
        request: FastAPI request object
        sig: Function signature to match parameters against
        include_body: Whether to include request body
        headers_to_include: Headers to extract from request
        
    Returns:
        Dictionary of gathered and type-cast parameters
    """
    params: Dict[str, Any] = {}

    # 1) Extract query parameters that match the function signature
    query_params = await gather_query_params(request, sig.parameters)
    params.update(query_params)

    # 2) Extract specified headers
    # Headers can be provided as list[str] or dict[str,str] mapping header->param name
    if headers_to_include:
        if isinstance(headers_to_include, dict):
            items = headers_to_include.items()
        else:
            # For list[str], use same name for header and parameter
            items = ((hdr, hdr) for hdr in headers_to_include)
        for hdr, param_name in items:
            raw = request.headers.get(hdr)
            if raw is not None:
                params[param_name] = raw

    # 3) Extract body parameters if requested
    if include_body:
        try:
            raw_body = await request.json()
        except Exception:
            raw_body = {}
        # Find parameter decorated with Body()
        body_param = next(
            (n for n, p in sig.parameters.items() if isinstance(p.default, BodyParam)),
            None
        )
        if body_param:
            # Handle nested body parameters
            if isinstance(raw_body, dict) and list(raw_body.keys()) == [body_param]:
                params[body_param] = raw_body[body_param]
            else:
                params[body_param] = raw_body

    # 4) Type cast parameters according to signature annotations
    for name, raw in list(params.items()):
        param = sig.parameters.get(name)
        ann = param.annotation if isinstance(param, inspect.Parameter) else inspect.Parameter.empty

        if raw is None or ann is inspect.Parameter.empty:
            continue

        try:
            # Cast to appropriate type based on annotation
            if ann is bool:
                params[name] = str(raw).lower() in ("true","1","yes","y")
            elif ann is int:
                params[name] = int(raw)
            elif ann is float:
                params[name] = float(raw)
            elif ann is str:
                params[name] = str(raw)
            else:
                params[name] = ann(raw)
        except Exception:
            # Keep original value if casting fails
            params[name] = raw

    return params


def determine_method_name(
    request: Request,
    explicit: Optional[str],
) -> str:
    """
    Determines which method name should handle the request.
    Returns explicit name if provided, otherwise uses endpoint function name.
    
    Args:
        request: FastAPI request object
        explicit: Optional explicit method name to use
    
    Returns:
        Method name string
    """
    if explicit:
        return explicit
    ep = request.scope.get("endpoint")
    if not ep:
        raise RuntimeError("No endpoint in scope")
    return ep.__name__


async def gather_query_params(
    request: Request,
    signature_params: Optional[Union[Iterable[str], Dict[str, Any]]] = None,
) -> Dict[str, Any]:
    """
    Extracts query parameters that match the function signature.
    
    Args:
        request: FastAPI request object
        signature_params: Parameter names/dict from function signature
        
    Returns:
        Dictionary of matched query parameters
    """
    data: Dict[str, Any] = {}
    if not signature_params:
        return data

    names = (
        signature_params.keys()
        if hasattr(signature_params, "keys")
        else signature_params
    )

    for name in names:
        if name == "self":
            continue
        if name in request.query_params:
            data[name] = request.query_params[name]

    return data
