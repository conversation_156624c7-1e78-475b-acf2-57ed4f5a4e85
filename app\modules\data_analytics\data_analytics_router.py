# Data Analytics Router - API endpoints for vessel data analytics

from fastapi import APIRouter, Depends
from auth import owner_vat_check

from app.general_logic_helper.controllers.data_controller import DataController
from app.general_logic_helper.handlers.measurements import Measurements
from app.modules.data_analytics.data_analytics_logic import DataAnalyticsLogic
from app.context.context import get_context

# Create router with data analytics tag
router = APIRouter(tags=["Data Analytics"])

# Set up dependency injection - no service layer, uses helpers directly
get_data_analytics_params, get_data_analytics_logic = get_context(
    DataAnalyticsLogic,
    None,
    Measurements,
    DataController,
)


@router.get("/data-analytics", dependencies=[Depends(owner_vat_check)])
async def data_analytics_logic(
    params: dict = Depends(get_data_analytics_params),
    logic: DataAnalyticsLogic = Depends(get_data_analytics_logic),
):
    # GET endpoint for data analytics - requires owner authentication
    return await logic.data_analytics_logic(**params)
