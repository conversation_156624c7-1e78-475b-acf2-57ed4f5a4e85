# Data Analytics Logic - handles business logic for vessel data analytics

from app.general_logic_helper.controllers.data_controller import DataController
from app.general_logic_helper.handlers.measurements import Measurements
from app.context.utils import rename_vat


class DataAnalyticsLogic:
    # Logic layer for data analytics operations

    def __init__(self, data_controller: DataController, measurement_data: Measurements):
        self.data_controller = data_controller
        self.measurements = measurement_data

    async def data_analytics_logic(
            self,
            from_date: str,
            owner_vat: str,
            to_date: str,
            vessel_id: int,
            vessel_imo: int,
            vessel_name: str
    ):
        # Process data analytics request - get measurements and serve via WebSocket
        params = rename_vat(**locals())
        redis_data = {"reports": {}}
        await self.measurements.get_and_set_measurements(params, redis_data)
        return await self.data_controller.wss_serve_data_analytics(redis_data, params)
