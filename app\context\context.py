import inspect
from typing import Type, TypeVar, Callable, Awaitable, <PERSON>, <PERSON><PERSON>, Optional
from fastapi import Depends, Request

from .dependencies import get_dependencies 
from .request_data import determine_method_name, _gather_and_cast_params

# Generic type variables for Service and Logic classes
S = TypeVar("S")
L = TypeVar("L")


def get_context(
    logic_cls: Type[L],          # The Logic class that handles business logic
    service_cls: Optional[Type[S]] = None,  # Optional Service class for data access
    *helper_classes: Type[Any],  # Additional helper classes needed
) -> Tuple[
    Callable[..., Awaitable[dict]],  # Returns a function that gathers request parameters
    Callable[..., L],                # Returns a function that creates Logic instances
]:
    # Get dependency injection setup for Logic class with its dependencies
    get_logic = get_dependencies(logic_cls, service_cls, *helper_classes)

    async def _get_request_params(
        request: Request,
        logic: L = Depends(get_logic),  # Inject Logic instance
    ) -> dict:
        # 1) Determine which Logic method will handle the request
        method_name = determine_method_name(request, explicit=None)
        fn = getattr(logic, method_name)
        sig = inspect.signature(fn)  # Get method's parameter signature

        # 2) Extract configuration flags from the endpoint decorator
        ep = request.scope.get("endpoint")
        include_body = getattr(ep, "include_body", False)  # Should request body be included
        headers_to_include = getattr(ep, "headers_to_include", None)  # Headers to extract

        # 3) Gather parameters from request and cast them to correct types
        return await _gather_and_cast_params(
            request,
            sig,
            include_body=include_body,
            headers_to_include=headers_to_include,
        )

    # Return tuple of parameter gatherer and logic factory functions
    return _get_request_params, get_logic
