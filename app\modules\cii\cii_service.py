# CII Service - handles data access for Carbon Intensity Indicator operations

import json
from typing import Union, List

from app.services.redis_api.redis_api_client_error import RedisApiError
from app.services.redis_api.redis_api_client import RedisApi


class CiiService:
    # Service layer for CII data operations

    def __init__(self, redis_client: RedisApi = None):
        # Initialize with Redis client (create new one if not provided)
        self.redis = redis_client or RedisApi()

    async def get_cii_data(self, payload):
        # Get CII data from Redis API
        return await self.redis.call(
            path="/cii",
            method="POST",
            json=payload,
        )

    async def get_cii_years(self, payload):
        # Get available CII years from Redis API
        return await self.redis.call(
            path="/cii-years",
            method="POST",
            json=payload
        )

    async def delete_key(self, key):
        # Delete a key from Redis
        return await self.redis.call(
            path="redis/delete",
            method="POST",
            json={"key": key},
        )

    async def scan_keys(self, key: str):
        # Scan for keys matching pattern in Redis
        return await self.redis.call(
            path="/redis/scan",
            pattern=key,
        )

    async def set_json_data(self, data: Union[dict, List[dict]]):
        # Set JSON data in Redis - accepts single dict or list of dicts
        if isinstance(data, dict):
            data = [data]

        if not data:
            raise RedisApiError("No data provided")
        if not isinstance(data, dict) and not all("value" in item for item in data):
            raise RedisApiError("All items must have a value")

        try:
            # Convert data to Redis-compatible format
            payload = [
                {"key": item.get("key"), "value": json.dumps(item.get("value"))}
                for item in data
            ]
            return await self.redis.call(
                path="redis",
                key="inserts",
                method="POST",
                json=payload,
                expected_status=201
            )
        except Exception as e:
            raise RedisApiError("Error while setting json data: %s")
