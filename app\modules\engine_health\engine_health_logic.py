# Engine Health Logic - handles business logic for engine health monitoring

from typing import Any

from app.modules.engine_health.engine_health_service import EngineHealthService
from app.context.utils import rename_vat


class EngineHealthLogic:
    # Logic layer for engine health monitoring operations
    # TODO: test with frontend

    def __init__(self, engine_health_service: EngineHealthService):
        self.engine_health_service = engine_health_service

    async def engine_health(self, owner_vat: str, vessel_imo: int) -> Any:
        # Get engine health data for vessel - rename VAT parameter and call service
        return await self.engine_health_service.engine_health(**rename_vat(**locals()))
