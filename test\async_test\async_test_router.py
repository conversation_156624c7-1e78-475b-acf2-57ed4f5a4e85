from fastapi import <PERSON><PERSON>out<PERSON>, Depends, Query

from test.async_test.async_test_logic import AsyncTestLogic
from app.context.context import get_context

router = APIRouter(prefix="/async-test", tags=["Async Testing"])

get_async_test_params, get_async_test_logic = get_context(
    AsyncTestLogic,
)


@router.get("/quick")
async def quick_response(
    params: dict = Depends(get_async_test_params),
    logic: AsyncTestLogic = Depends(get_async_test_logic),
):
    """
    Quick response endpoint that returns immediately.
    This should not be blocked by other long-running endpoints.
    
    Use this to test that async functionality works correctly.
    """
    return await logic.quick_response(**params)


@router.get("/long-sleep")
async def long_sleep(
    params: dict = Depends(get_async_test_params),
    logic: AsyncTestLogic = Depends(get_async_test_logic),
):
    """
    Sleeps for more than 10 seconds before returning data.
    
    Test scenario:
    1. Start this endpoint
    2. Immediately call /quick endpoint
    3. The quick endpoint should respond immediately, not wait for this one
    """
    return await logic.long_sleep(**params)


@router.get("/medium-sleep")
async def medium_sleep(
    params: dict = Depends(get_async_test_params),
    logic: AsyncTestLogic = Depends(get_async_test_logic),
):
    """
    Sleeps for 10 seconds before returning data.
    
    Test scenario:
    1. Start /very-long-sleep endpoint (25s)
    2. Start this endpoint (10s)
    3. This should complete twice before very-long-sleep completes once
    """
    return await logic.medium_sleep(**params)


@router.get("/very-long-sleep")
async def very_long_sleep(
    params: dict = Depends(get_async_test_params),
    logic: AsyncTestLogic = Depends(get_async_test_logic),
):
    """
    Sleeps for 25 seconds before returning data.
    
    Test scenario:
    1. Start this endpoint first
    2. Start /medium-sleep endpoint
    3. Medium sleep should complete at least twice before this completes
    """
    return await logic.very_long_sleep(**params)


@router.get("/load-test")
async def load_test_endpoint(
    params: dict = Depends(get_async_test_params),
    logic: AsyncTestLogic = Depends(get_async_test_logic),
):
    """
    Endpoint designed for load testing.
    
    Test scenario:
    1. Send many concurrent requests to this endpoint
    2. Monitor response times and success rates
    3. Check if the server handles the load gracefully
    """
    return await logic.load_test_endpoint(**params)


@router.get("/custom-sleep")
async def custom_sleep(
    sleep_seconds: int = Query(5, ge=1, le=60, description="Number of seconds to sleep (1-60)"),
    params: dict = Depends(get_async_test_params),
    logic: AsyncTestLogic = Depends(get_async_test_logic),
):
    """
    Sleeps for a custom duration specified in the query parameter.
    
    Parameters:
    - sleep_seconds: Number of seconds to sleep (1-60)
    
    Test scenario:
    1. Call with different sleep durations
    2. Test concurrent requests with various timings
    """
    return await logic.custom_sleep(sleep_seconds=sleep_seconds, **params)


@router.get("/health")
async def health_check():
    """
    Simple health check endpoint for testing.
    Returns immediately without any processing.
    """
    return {
        "status": "healthy",
        "message": "Async test module is running",
        "endpoints": [
            "/async-test/quick",
            "/async-test/long-sleep", 
            "/async-test/medium-sleep",
            "/async-test/very-long-sleep",
            "/async-test/load-test",
            "/async-test/custom-sleep",
            "/async-test/health"
        ]
    }
