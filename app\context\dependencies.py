import inspect
from typing import Type, TypeVar, Callable, Optional, Any, List, get_type_hints, Dict
from fastapi import Depends

# Define generic type variables for type hinting
S = TypeVar("S")  # Service type - represents any Service class
L = TypeVar("L")  # Logic type - represents any Logic class


def get_instance(cls: Type[Any]) -> Callable[[], Any]:
    """
    Creates a simple factory function that instantiates a class with no dependencies.
    Used for classes that don't require any constructor parameters.
    """
    def _get_instance() -> Any:
        return cls()  # Simply creates a new instance
    return _get_instance


def get_dependencies(
    logic_cls: Type[L],          # The Logic class that needs dependencies
    service_cls: Optional[Type[S]] = None,  # Optional Service class dependency
    *helper_classes: Type[Any],  # Any additional helper class dependencies
) -> Callable[..., L]:
    """
    Creates a FastAPI dependency injection factory for Logic classes.
    
    This function:
    1. Analyzes the Logic class constructor
    2. Maps dependencies to their factory functions
    3. Creates a dependency injection wrapper
    """
    # 1) Get constructor parameters (excluding 'self')
    init_sig = inspect.signature(logic_cls.__init__)
    init_params = list(init_sig.parameters.values())[1:]  # Skip 'self'

    # 2) Create mapping of injectable types to their factory functions
    injectables: Dict[Type[Any], Callable[..., Any]] = {}

    # Add service class if provided
    if service_cls:
        injectables[service_cls] = get_instance(service_cls)

    # Process helper classes
    for helper in helper_classes:
        if service_cls:
            # If service exists, recursively wire helper with service
            injectables[helper] = get_dependencies(helper, service_cls)
        else:
            # Otherwise create simple factory
            injectables[helper] = get_instance(helper)

    # 3) Create FastAPI dependency parameters for injectable constructor args
    dep_params: List[inspect.Parameter] = []
    type_hints = get_type_hints(logic_cls.__init__)
    
    for p in init_params:
        # Get parameter type annotation
        ann = type_hints.get(p.name, p.annotation)
        factory = injectables.get(ann)
        
        if factory:
            # Create FastAPI Depends parameter
            dep_params.append(
                inspect.Parameter(
                    name=p.name,
                    kind=inspect.Parameter.POSITIONAL_OR_KEYWORD,
                    annotation=ann,
                    default=Depends(factory),  # Use factory as dependency
                )
            )

    # 4) Create the dependency injection wrapper function
    wrapper_sig = inspect.Signature(dep_params)

    def _get_logic(**kwargs) -> L:
        return logic_cls(**kwargs)  # Create Logic instance with injected deps

    # Attach signature for FastAPI to recognize dependencies
    _get_logic.__signature__ = wrapper_sig
    return _get_logic
