# User Router - API endpoints for user management operations

from fastapi import APIRouter, Depends

from app.context.utils import route_input
from auth import owner_vat_check
from app.modules.user.user_logic import UserLogic
from app.modules.user.user_service import UserService
from app.context.context import get_context

# Create router with users prefix and tag
router = APIRouter(prefix="/users", tags=["Users"])

# Set up dependency injection for logic and service layers
get_user_params, get_user_logic = get_context(
    UserLogic,
    UserService,
)


@router.get("", dependencies=[Depends(owner_vat_check)])
async def users(
        params: dict = Depends(get_user_params),
        logic: UserLogic = Depends(get_user_logic),
):
    # GET endpoint to retrieve all users for an owner
    return await logic.users(**params)


@router.get("/me", dependencies=[Depends(owner_vat_check)])
@route_input(headers_to_include={"X-User-Email": "email"})    # Extract email from header
async def users_me(
    params: dict = Depends(get_user_params),
    logic: UserLogic = Depends(get_user_logic),
):
    # GET endpoint to retrieve current user profile
    return await logic.users_me(**params)
