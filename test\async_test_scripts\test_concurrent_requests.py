#!/usr/bin/env python3
"""
Test script for concurrent async requests using REAL endpoints.
This script tests that FastAPI handles concurrent requests properly using actual production routes.
"""

import asyncio
import aiohttp
import time
import jwt
from typing import List, Dict, Any
from datetime import datetime, timedelta
from test_config import get_test_config, get_endpoints, validate_config, print_config_help


class AsyncTester:
    def __init__(self, base_url: str = "http://localhost:8769", jwt_secret: str = None, test_config: dict = None):
        self.base_url = base_url
        self.session = None
        self.jwt_secret = jwt_secret or "your-jwt-secret-key"  # Should match your .env JWT_SECRET
        self.test_config = test_config or self._default_test_config()

    def _default_test_config(self):
        """Default test configuration with sample data."""
        return {
            "owner_vat": "NO950700939",  # Replace with actual test owner VAT
            "session_owner_vat": "NO950700939",  # Replace with actual session owner VAT
            "vessel_imo": "8918461",  # Replace with actual test vessel IMO
            "vessel_id": 32,  # Replace with actual test vessel ID
            "vessel_name": "Wilson Saga",  # Replace with actual vessel name
            "from_date": "2024-01-01 00:00:00",
            "to_date": "2024-01-07 23:59:59",
            "start_date": "2024-01-01",
            "end_date": "2024-01-07"
        }

    def _generate_jwt_token(self):
        """Generate a JWT token for authentication."""
        payload = {
            "exp": datetime.utcnow() + timedelta(hours=1),
            "iat": datetime.utcnow(),
            "test": True
        }
        return jwt.encode(payload, self.jwt_secret, algorithm="HS256")

    async def __aenter__(self):
        # Create headers with authentication
        headers = {
            "X-API-Key": self._generate_jwt_token(),
            "X-Tenant-Owner-Vat": self.test_config["session_owner_vat"],
            "Content-Type": "application/json"
        }

        self.session = aiohttp.ClientSession(headers=headers)
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()

    async def make_request(self, endpoint: str, params: dict = None, request_id: int = None) -> Dict[str, Any]:
        """Make a single request to an endpoint."""
        url = f"{self.base_url}{endpoint}"
        start_time = time.time()

        # Merge default params with provided params
        request_params = {**self.test_config}
        if params:
            request_params.update(params)

        try:
            async with self.session.get(url, params=request_params) as response:
                try:
                    data = await response.json()
                except:
                    data = await response.text()

                end_time = time.time()

                return {
                    "request_id": request_id,
                    "endpoint": endpoint,
                    "status_code": response.status,
                    "response_time": round(end_time - start_time, 2),
                    "data": data if response.status == 200 else f"Error: {data}",
                    "timestamp": end_time,
                    "success": response.status == 200
                }
        except Exception as e:
            end_time = time.time()
            return {
                "request_id": request_id,
                "endpoint": endpoint,
                "status_code": None,
                "response_time": round(end_time - start_time, 2),
                "error": str(e),
                "timestamp": end_time,
                "success": False
            }

    async def test_basic_async_functionality(self):
        """
        Test 1: Basic async functionality using real endpoints
        - Start heavy data-analytics endpoint (slow)
        - Immediately call quick endpoints (owners, vessels)
        - Quick endpoints should respond immediately
        """
        print("🧪 Test 1: Basic Async Functionality (Real Endpoints)")
        print("Starting heavy data-analytics endpoint, then calling quick endpoints...")

        start_time = time.time()

        # Start all requests concurrently
        tasks = [
            self.make_request("/data-analytics", None, 1),  # Heavy endpoint
            self.make_request("/owners", None, 2),          # Quick endpoint
            self.make_request("/vessels", None, 3),         # Quick endpoint
            self.make_request("/owners", None, 4),          # Quick endpoint
            self.make_request("/vessels", None, 5),         # Quick endpoint
        ]
        
        results = await asyncio.gather(*tasks)
        
        # Sort results by completion time
        results.sort(key=lambda x: x["timestamp"])
        
        print(f"\nResults (completed in order):")
        for result in results:
            status = "✅" if result.get("success", False) else "❌"
            if "error" in result:
                print(f"{status} Request {result['request_id']} ({result['endpoint']}): ERROR - {result['error']}")
            else:
                print(f"{status} Request {result['request_id']} ({result['endpoint']}): {result['response_time']}s - Status: {result['status_code']}")

        # Check if quick requests completed before heavy request
        quick_requests = [r for r in results if r["endpoint"] in ["/owners", "/vessels"]]
        heavy_requests = [r for r in results if r["endpoint"] in ["/data-analytics"]]

        if quick_requests and heavy_requests:
            quick_max_time = max(r["response_time"] for r in quick_requests if r.get("success", False))
            heavy_min_time = min(r["response_time"] for r in heavy_requests if r.get("success", False))

            if quick_max_time < 2:
                print("✅ PASS: Quick requests completed immediately while heavy request was running")
            else:
                print("❌ FAIL: Quick requests took too long - async functionality may not be working correctly")
        
        print(f"Total test time: {time.time() - start_time:.2f}s\n")

    async def test_multiple_heavy_endpoints(self):
        """
        Test 2: Multiple heavy endpoints with different processing times
        - Start data-analytics (heavy processing)
        - Start hull-performance endpoints (medium processing)
        - Test concurrent heavy operations
        """
        print("🧪 Test 2: Multiple Heavy Endpoints")
        print("Starting data-analytics, then hull-performance endpoints...")

        start_time = time.time()

        # Start very heavy endpoint first
        heavy_task = asyncio.create_task(self.make_request("/data-analytics", None, 1))

        # Wait a moment, then start medium heavy requests
        await asyncio.sleep(1)

        medium_task1 = asyncio.create_task(self.make_request("/hull-performance-ai", None, 2))
        medium_task2 = asyncio.create_task(self.make_request("/hull-performance-raw", None, 3))
        
        # Wait for all to complete
        results = await asyncio.gather(heavy_task, medium_task1, medium_task2)

        # Sort by completion time
        results.sort(key=lambda x: x["timestamp"])

        print(f"\nResults (completed in order):")
        for result in results:
            status = "✅" if result.get("success", False) else "❌"
            if "error" in result:
                print(f"{status} Request {result['request_id']} ({result['endpoint']}): ERROR - {result['error']}")
            else:
                print(f"{status} Request {result['request_id']} ({result['endpoint']}): {result['response_time']}s - Status: {result['status_code']}")

        # Analyze concurrent processing
        successful_results = [r for r in results if r.get("success", False)]
        if len(successful_results) >= 2:
            print("✅ PASS: Multiple heavy endpoints processed concurrently")
        else:
            print("❌ FAIL: Heavy endpoints may be blocking each other")
        
        print(f"Total test time: {time.time() - start_time:.2f}s\n")

    async def test_load_testing(self, num_requests: int = 20):
        """
        Test 3: Load testing using real endpoints
        - Send many concurrent requests to various endpoints
        - Check response times and success rates
        """
        print(f"🧪 Test 3: Load Testing ({num_requests} concurrent requests)")
        print("Sending multiple concurrent requests to real endpoints...")

        start_time = time.time()

        # Create many concurrent requests to different endpoints
        endpoints = ["/owners", "/vessels", "/data-analytics", "/hull-performance-ai"]
        tasks = []

        for i in range(num_requests):
            endpoint = endpoints[i % len(endpoints)]  # Rotate through endpoints
            tasks.append(self.make_request(endpoint, None, i+1))
        
        results = await asyncio.gather(*tasks)

        # Analyze results
        successful_requests = [r for r in results if r.get("success", False)]
        failed_requests = [r for r in results if not r.get("success", False)]
        
        if successful_requests:
            response_times = [r["response_time"] for r in successful_requests]
            avg_response_time = sum(response_times) / len(response_times)
            max_response_time = max(response_times)
            min_response_time = min(response_times)
        else:
            avg_response_time = max_response_time = min_response_time = 0
        
        print(f"\nLoad Test Results:")
        print(f"✅ Successful requests: {len(successful_requests)}/{num_requests}")
        print(f"❌ Failed requests: {len(failed_requests)}")
        print(f"📊 Response times - Avg: {avg_response_time:.2f}s, Min: {min_response_time:.2f}s, Max: {max_response_time:.2f}s")
        
        if len(successful_requests) == num_requests:
            print("✅ PASS: All requests completed successfully")
        elif len(successful_requests) >= num_requests * 0.9:
            print("⚠️  PARTIAL: Most requests completed successfully")
        else:
            print("❌ FAIL: Many requests failed - may need rate limiting")
        
        print(f"Total test time: {time.time() - start_time:.2f}s\n")


async def main():
    """Run all async tests."""
    print("🚀 Starting FastAPI Async Functionality Tests (Real Endpoints)")

    # Load configuration
    test_config = get_test_config()

    # Validate configuration
    config_issues = validate_config()
    if config_issues:
        print("❌ Configuration issues found:")
        for issue in config_issues:
            print(f"   - {issue}")
        print()
        print_config_help()
        return

    print(f"Server: {test_config['base_url']}")
    print(f"Owner VAT: {test_config['owner_vat']}")
    print(f"Vessel IMO: {test_config['vessel_imo']}")
    print("⚠️  Make sure your FastAPI server is running and test data is valid!\n")

    async with AsyncTester(
        base_url=test_config["base_url"],
        jwt_secret=test_config["jwt_secret"],
        test_config=test_config
    ) as tester:
        # Test server availability with a simple endpoint
        try:
            health_result = await tester.make_request("/owners")
            if not health_result.get("success", False):
                print("❌ Server not available or authentication failed.")
                print("Make sure:")
                print("  1. FastAPI server is running on http://localhost:8769")
                print("  2. JWT_SECRET matches your .env file")
                print("  3. Test data (owner_vat, vessel_imo, etc.) is valid")
                print(f"  Error: {health_result.get('error', 'Unknown error')}")
                return
            print("✅ Server is available and authenticated\n")
        except Exception as e:
            print(f"❌ Cannot connect to server: {e}")
            return

        # Run all tests
        await tester.test_basic_async_functionality()
        await tester.test_multiple_heavy_endpoints()
        await tester.test_load_testing(20)

        print("🎉 All tests completed!")


if __name__ == "__main__":
    asyncio.run(main())
