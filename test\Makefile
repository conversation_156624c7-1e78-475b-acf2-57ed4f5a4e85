# Makefile for FastAPI Backend Testing

.PHONY: help install test test-unit test-integration test-system test-performance test-all test-quick test-ci clean coverage lint format check-env

# Default target
help:
	@echo "FastAPI Backend Test Commands"
	@echo "============================="
	@echo ""
	@echo "Setup Commands:"
	@echo "  install          Install all dependencies"
	@echo "  install-test     Install test dependencies only"
	@echo "  check-env        Check test environment setup"
	@echo ""
	@echo "Test Commands:"
	@echo "  test             Run all tests"
	@echo "  test-unit        Run unit tests"
	@echo "  test-integration Run integration tests"
	@echo "  test-system      Run system tests"
	@echo "  test-performance Run performance tests"
	@echo "  test-load        Run load tests"
	@echo "  test-stress      Run stress tests"
	@echo "  test-quick       Run quick test suite (unit + basic integration)"
	@echo "  test-ci          Run CI test suite"
	@echo ""
	@echo "Coverage Commands:"
	@echo "  coverage         Run tests with coverage report"
	@echo "  coverage-html    Generate HTML coverage report"
	@echo "  coverage-xml     Generate XML coverage report"
	@echo ""
	@echo "Quality Commands:"
	@echo "  lint             Run linting checks"
	@echo "  format           Format code"
	@echo "  security         Run security checks"
	@echo ""
	@echo "Utility Commands:"
	@echo "  clean            Clean test artifacts"
	@echo "  clean-cache      Clean Python cache files"
	@echo ""
	@echo "Examples:"
	@echo "  make test-unit           # Run unit tests"
	@echo "  make coverage-html       # Generate HTML coverage report"
	@echo "  make test-quick          # Run quick test suite"

# Installation targets
install:
	pip install -r requirements.txt
	pip install -r test/requirements.txt

install-test:
	pip install -r test/requirements.txt

# Environment check
check-env:
	python scripts/run_tests.py --check-env

# Test targets
test:
	python scripts/run_tests.py --all

test-unit:
	python scripts/run_tests.py --unit

test-integration:
	python scripts/run_tests.py --integration

test-system:
	python scripts/run_tests.py --system

test-performance:
	python scripts/run_tests.py --performance

test-load:
	python scripts/run_tests.py --load

test-stress:
	python scripts/run_tests.py --stress

test-quick:
	python scripts/run_tests.py --quick

test-ci:
	python scripts/run_tests.py --ci

# Coverage targets
coverage:
	python scripts/run_tests.py --unit --coverage

coverage-html:
	pytest --cov=app --cov-report=html
	@echo "Coverage report generated in htmlcov/index.html"

coverage-xml:
	pytest --cov=app --cov-report=xml
	@echo "Coverage report generated in coverage.xml"

# Verbose test targets
test-unit-verbose:
	python scripts/run_tests.py --unit --verbose

test-integration-verbose:
	python scripts/run_tests.py --integration --verbose

test-system-verbose:
	python scripts/run_tests.py --system --verbose

# Parallel test targets
test-parallel:
	python scripts/run_tests.py --all --parallel

test-unit-parallel:
	pytest test/unit/ -n auto

# Specific test targets
test-auth:
	pytest test/unit/test_auth.py -v

test-redis:
	pytest test/unit/test_redis_client.py test/integration/test_redis_integration.py -v

test-api:
	pytest test/integration/test_api_endpoints.py -v

test-e2e:
	pytest test/system/test_end_to_end.py -v

# Quality targets
lint:
	flake8 app/ test/ --max-line-length=120
	pylint app/ --disable=C0114,C0115,C0116
	mypy app/ --ignore-missing-imports

format:
	black app/ test/ --line-length=120
	isort app/ test/

security:
	bandit -r app/ -f json -o bandit-report.json
	safety check --json --output safety-report.json
	@echo "Security reports generated: bandit-report.json, safety-report.json"

# Cleanup targets
clean:
	rm -rf .pytest_cache/
	rm -rf htmlcov/
	rm -rf .coverage
	rm -f coverage.xml
	rm -f test-results.xml
	rm -f unit-test-results.xml
	rm -f integration-test-results.xml
	rm -f system-test-results.xml
	rm -f performance-test-results.xml
	rm -f bandit-report.json
	rm -f safety-report.json

clean-cache:
	find . -type d -name "__pycache__" -exec rm -rf {} +
	find . -type f -name "*.pyc" -delete
	find . -type f -name "*.pyo" -delete

# Development targets
dev-setup: install
	pre-commit install

test-watch:
	pytest-watch test/unit/ -- -v

# Docker targets (if using Docker for testing)
docker-test:
	docker-compose -f docker-compose.test.yml up --build --abort-on-container-exit

docker-test-clean:
	docker-compose -f docker-compose.test.yml down -v

# Database targets (if using test database)
test-db-setup:
	@echo "Setting up test database..."
	# Add database setup commands here

test-db-teardown:
	@echo "Tearing down test database..."
	# Add database teardown commands here

# Performance monitoring
test-performance-monitor:
	pytest test/system/test_performance_load.py -m performance --benchmark-only

# Test data generation
generate-test-data:
	python scripts/generate_test_data.py

# Continuous testing
test-continuous:
	while true; do \
		make test-quick; \
		sleep 30; \
	done

# Test reporting
test-report:
	pytest --html=test-report.html --self-contained-html
	@echo "Test report generated: test-report.html"

# Integration with external services
test-with-redis:
	docker run -d --name test-redis -p 6379:6379 redis:7-alpine
	sleep 5
	make test-integration
	docker stop test-redis
	docker rm test-redis

# Load testing with external tools
load-test-locust:
	locust -f test/load/locustfile.py --host=http://localhost:8000

# Test environment validation
validate-test-env:
	python -c "import pytest, httpx, coverage, factory_boy; print('✅ All test dependencies available')"

# Quick development cycle
dev-test: format lint test-quick

# Full CI simulation
ci-simulation: clean install test-ci security coverage-xml

# Test specific modules
test-home:
	pytest test/unit/test_home_logic.py test/unit/test_home_service.py test/integration/test_api_endpoints.py::TestHomeEndpoints -v

test-user:
	pytest test/unit/test_user*.py test/integration/test_api_endpoints.py::TestUserEndpoints -v

test-auth:
	pytest test/unit/test_auth.py test/integration/test_api_endpoints.py::TestAuthenticationFlows -v

# Debug targets
debug-test:
	pytest --pdb --pdbcls=IPython.terminal.debugger:Pdb

debug-test-specific:
	pytest $(TEST) --pdb --pdbcls=IPython.terminal.debugger:Pdb -s

# Test with different Python versions (if using pyenv)
test-python-versions:
	for version in 3.9 3.10 3.11; do \
		echo "Testing with Python $$version"; \
		pyenv local $$version; \
		make test-quick; \
	done
