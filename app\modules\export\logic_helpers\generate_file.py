import os

import asyncio
import base64
import logging
from datetime import datetime
from typing import Any, Union

import orjson
import pandas as pd

from app.general_logic_helper.helpers.excel.excel_helper import ExcelHelper
from app.services.redis_api.redis_api_client import RedisApi
from app.general_logic_helper.utils.special_vessel_keys import (
    aux_do_vessel_id_key_relation,
    aux_do_vessel_imo_key_relation,
)


async def _merge_vessel_and_anomaly_data(
    vessel_df: pd.DataFrame, anomalies_df: pd.DataFrame
) -> pd.DataFrame:
    """Merge vessel and anomaly data.

    Args:
        vessel_df: DataFrame containing vessel data
        anomalies_df: DataFrame containing anomaly data

    Returns:
        pd.DataFrame: DataFrame containing merged vessel and anomaly data
    """
    merged_df = pd.merge(
        left=vessel_df,
        right=anomalies_df,
        how="left",
        on="timestamp",
    )
    merged_df["has_anomalies"] = merged_df["has_anomalies"].fillna("None")
    return merged_df


async def _prepare_excel_data(
    vessel_df: pd.DataFrame,
    anomalies_df: pd.DataFrame,
    exclude_anomalies: bool,
) -> pd.DataFrame:
    """Prepare the final DataFrame for Excel generation.

    Args:
        vessel_df: DataFrame containing vessel data
        anomalies_df: DataFrame containing anomaly data
        exclude_anomalies: bool indicating whether to exclude anomalies

    Returns:
        pd.DataFrame: DataFrame containing the final data for Excel generation
    """
    if anomalies_df.empty:
        vessel_df["has_anomalies"] = False
        return vessel_df

    merged_df = await _merge_vessel_and_anomaly_data(vessel_df, anomalies_df)

    if exclude_anomalies:
        return merged_df[merged_df["has_anomalies"] == False]

    return merged_df


class GenerateFile:
    def __init__(self):
        self.redis_api = RedisApi()
        self.cols_to_drop = [
            "flow_totals",
            "vessel",
            "frugal_alarms",
            "fuel_type",
            "thrust_cmd",
            "rpm_cmd",
            "pitch_cmd",
            "prop_1_pitch",
            "consumption",
            "year",
            "week_number",
        ]
        self.boiler_add_groups = [
            20,
            23,
            25,
            28,
        ]  # Alice Theresa, Tina Theresa, Annelise Theresa, Karina Theresa

    async def generate_json(
        self,
        request_data: dict,
        **kwargs,
    ) -> list[dict]:
        """Generate JSON data for vessel measurements and anomalies.

        Args:
            request_data: dict containing request data

        Returns:
            str: JSON string containing merged vessel and anomaly data, or "Error" if processing fails
        """
        try:
            vessel_imo = request_data.get("vessel_imo")

            vessel_df = await self._get_vessel_measurements(
                request_data, vessel_id=None, vessel_imo=vessel_imo
            )

            if vessel_df.empty:
                return "Error"

            anomalies_df = await self._get_processed_anomalies(request_data)

            if not anomalies_df.empty:
                vessel_df = await _merge_vessel_and_anomaly_data(
                    vessel_df, anomalies_df
                )

            measurements = vessel_df.to_dict(orient="records")
            return measurements

        except Exception as e:
            logging.error(f"Error generating JSON file: {e}")
            return "Error"

    async def generate_cylinder_json(
        self,
        request_data: dict,
        **kwargs,
    ) -> str:
        """Generate JSON data for vessel cylinder temperatures.

        Args:
            request_data: dict containing request data

        Returns:
            str: JSON string containing cylinder temperature data, or "Error" if processing fails
        """
        try:
            request_data["type"] = "cylinder_temperature"
            cylinder_temp = (
                await self.redis_api.retrieve_time_series_data(request_data)
            ).get("cylinder_temperature", [])
            return cylinder_temp

        except Exception as e:
            logging.error(f"Error generating JSON file: {e}")
            return "Error"

    async def _get_vessel_measurements(
        self, request_data: dict, vessel_id: int | None, vessel_imo: int | None
    ) -> pd.DataFrame:
        """Fetch and process vessel measurements.

        Args:
            request_data: dict containing request data

        Returns:
            pd.DataFrame: DataFrame containing vessel measurements
        """
        request_data["type"] = "measurements"
        measurements = (
            await self.redis_api.retrieve_time_series_data(request_data)
        ).get("measurements", [])
        draft_min = 0.0
        draft_max = 10000.0
        min_rpm = 0.0
        max_fc = 50000.0

        try:
            df = pd.DataFrame(measurements)
            filtered_df = df[
                (
                    ((df["draft_aft"] + df["draft_fwd"]) / 2).between(
                        draft_min, draft_max
                    )
                )
                & (df["me_1_rpm"] >= min_rpm)
                & (df["me_1_fc_mass"] < max_fc)
            ]

            # Pull out diesel data and make new column
            if vessel_id in aux_do_vessel_id_key_relation.keys():
                aux_do_key = aux_do_vessel_id_key_relation[vessel_id]
                filtered_df['aux_do_fc_mass'] = filtered_df['consumption'].apply(lambda x: "None" if x is None else (0 if x[aux_do_key] < 0 else x[aux_do_key]))

            elif vessel_imo in aux_do_vessel_imo_key_relation.keys():
                aux_do_key = aux_do_vessel_imo_key_relation[vessel_imo]
                filtered_df['aux_do_fc_mass'] = filtered_df['consumption'].apply(lambda x: "None" if x is None else (0 if x[aux_do_key] < 0 else x[aux_do_key]))

            if not filtered_df.empty:
                filtered_df["timestamp"] = pd.to_datetime(filtered_df["timestamp"], format='ISO8601')
                filtered_df.drop(
                    columns=self.cols_to_drop, errors="ignore", inplace=True
                )
            return filtered_df
        except Exception as e:
            logging.error(f"Error processing vessel measurements: {e}")
            return pd.DataFrame()

    def _generate_excel_sheet(
        self,
        vessel_name: str,
        data: pd.DataFrame,
        vessel_id: str,
        sorted_type: bool,
    ) -> dict[str, str]:
        """Generate and encode Excel sheet.

        Args:
            vessel_name: str containing vessel name
            data: DataFrame containing vessel measurements
            vessel_id: str containing vessel ID
            sorted_type: bool indicating whether to sort the data

        Returns:
            str: Base64 encoded Excel file as JSON string
        """
        xl_hlp = ExcelHelper()
        boiler_groups: bool = vessel_id in self.boiler_add_groups

        sheet_blob = xl_hlp.generate_data_sheet_from_database(
            name=vessel_name,
            vessel_data=data,
            _vessel_id=vessel_id,
            boiler_add_groups=boiler_groups,
            _sorted_type=sorted_type,
        )

        if sheet_blob is None:
            return "Error"

        encoded: str = base64.b64encode(sheet_blob).decode("utf-8")
        return {"excel": encoded}

    async def _get_processed_anomalies(self, request_data: dict) -> pd.DataFrame:
        """Fetch and process anomaly data.

        Args:
            request_data: dict containing request data

        Returns:
            pd.DataFrame: DataFrame containing anomaly data
        """
        request_data["type"] = "anomalies"
        anomalies = (await self.redis_api.retrieve_time_series_data(request_data)).get(
            "anomalies", []
        )
        if not anomalies:
            return pd.DataFrame()

        df = pd.DataFrame(anomalies)
        df["timestamp"] = pd.to_datetime(df["timestamp"])
        df["has_anomalies"] = df["is_frozen"] | df["is_outlier"]

        return (
            df[["timestamp", "has_anomalies"]]
            .drop_duplicates(subset="timestamp", keep="first")
            .reset_index(drop=True)
        )

    async def generate_excel(
        self,
        request_data: dict,
        **kwargs,
    ) -> str | dict[str, str]:
        """Generate Excel file containing vessel measurements and anomalies.

        Args:
            request_data: dict containing request data

        Returns:
            str: Base64 encoded Excel file as JSON string, or "Error" if processing fails
        """
        try:
            exclude_anomalies = request_data.get("exclude_anomalies", False)
            sorted_type = request_data.get("sorted_type", False)
            vessel_id = request_data.get("vessel_id")
            vessel_name = request_data.get("vessel_name")
            vessel_df = await self._get_vessel_measurements(
                request_data, vessel_id=vessel_id, vessel_imo=None
            )

            if vessel_df.empty:
                return "Error"

            anomalies_df = await self._get_processed_anomalies(request_data)

            final_df = await _prepare_excel_data(
                vessel_df, anomalies_df, exclude_anomalies
            )
            return self._generate_excel_sheet(
                vessel_name=vessel_name,
                data=final_df,
                vessel_id=vessel_id,
                sorted_type=sorted_type,
            )

        except Exception as e:
            logging.error(f"Error generating Excel file: {e}")
            return "Error"

    @staticmethod
    def json_serial(obj: Any) -> Union[str, bytes, None]:
        """JSON serializer for objects not serializable by default json code."""
        if isinstance(obj, (datetime, pd.Timestamp)):
            return obj.isoformat()
        raise TypeError(f"Type {type(obj)} not serializable")


if __name__ == "__main__":
    generate_file = GenerateFile()
    request = {
        "vessel_imo": "9129122",
        "selected_owner_vat": "FO383031",
        "from_date": "2023-06-01 00:00:00",
        "to_date": "2023-06-02 23:59:59",
        "exclude_anomalies": False,
        "sorted_type": "asc",
        "vessel_id": "1",
        "vessel_name": "Hav Atlantic",
    }

    if not os.path.exists("json_file.json"):
        json_file = asyncio.run(generate_file.generate_json(request))  # noqa
        with open("json_file.json", "w") as f:
            f.write(json_file)

    if not os.path.exists("excel_file.xlsx"):
        excel_data = asyncio.run(generate_file.generate_excel(request))  # noqa
        excel_file = orjson.loads(excel_data).get("excel")

        # Save as Excel file
        blob = base64.b64decode(excel_file)
        with open("excel_file.xlsx", "wb") as f:
            f.write(blob)
