# Efficiency Router - API endpoints for vessel efficiency calculations

from fastapi import APIRouter, Depends
from auth import owner_vat_check

from app.general_logic_helper.controllers.data_controller import DataController
from app.modules.efficiency.efficiency_logic import EfficiencyLogic
from app.context.context import get_context
from app.context.utils import route_input

# Create router with efficiency tag
router = APIRouter(tags=["Efficiency"])

# Set up dependency injection - no service layer, uses data controller directly
get_efficiency_params, get_efficiency_logic = get_context(
    EfficiencyLogic,
    None,
    DataController,
)


@router.post("/efficiency", dependencies=[Depends(owner_vat_check)])
@route_input(include_body=True)
async def efficiency_logic(
    params: dict = Depends(get_efficiency_params),
    logic: EfficiencyLogic = Depends(get_efficiency_logic),
):
    # POST endpoint for efficiency calculations - requires owner authentication and body data
    return await logic.efficiency_logic(**params)
