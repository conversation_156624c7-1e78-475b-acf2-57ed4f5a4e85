# MRV Router - API endpoints for MRV (Monitoring, Reporting, Verification) operations

from fastapi import APIRouter, Depends
from auth import owner_vat_check

from app.modules.mrv.logic_helpers.mrv_helper import Mrv<PERSON>elper
from app.modules.mrv.mrv_logic import MrvLogic
from app.modules.mrv.mrv_service import MrvService
from app.context.context import get_context
from app.context.utils import route_input

# Create router with MRV prefix and tag
router = APIRouter(prefix="/mrv", tags=["MRV"])

# Set up dependency injection for logic, service, and helper layers
get_mrv_params, get_mrv_logic = get_context(
    MrvLogic,
    MrvService,
    MrvHelper,
)


@router.get("", dependencies=[Depends(owner_vat_check)])
async def mrv_data(
    params: dict = Depends(get_mrv_params),
    logic: MrvLogic = Depends(get_mrv_logic),
):
    # GET endpoint to retrieve MRV data with processing
    return await logic.mrv_data(**params)


@router.get("/report", dependencies=[Depends(owner_vat_check)])
async def mrv_report(
    params: dict = Depends(get_mrv_params),
    logic: MrvLogic = Depends(get_mrv_logic),
):
    # GET endpoint to generate MRV report
    return await logic.mrv_report(**params)


@router.get("/report/voyage", dependencies=[Depends(owner_vat_check)])
async def voyage_report(
    params: dict = Depends(get_mrv_params),
    logic: MrvLogic = Depends(get_mrv_logic),
):
    # GET endpoint to generate PDF report for specific voyage
    return await logic.voyage_report(**params)


@router.post("/voyage", dependencies=[Depends(owner_vat_check)])
@route_input(include_body=True)
async def create_voyage(
    params: dict = Depends(get_mrv_params),
    logic: MrvLogic = Depends(get_mrv_logic),
):
    # POST endpoint to create new voyage record
    return await logic.create_voyage(**params)


@router.put("/voyage", dependencies=[Depends(owner_vat_check)])
@route_input(include_body=True)
async def update_voyage(
        params: dict = Depends(get_mrv_params),
        logic: MrvLogic = Depends(get_mrv_logic),
):
    # PUT endpoint to update existing voyage record
    return await logic.update_voyage(**params)


@router.delete("/voyage", dependencies=[Depends(owner_vat_check)])
async def delete_voyage(
        params: dict = Depends(get_mrv_params),
        logic: MrvLogic = Depends(get_mrv_logic),
):
    # DELETE endpoint to remove voyage record
    return await logic.delete_voyage(**params)
