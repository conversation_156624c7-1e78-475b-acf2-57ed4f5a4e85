"""
Unit tests for HomeService class.
"""
import pytest
from unittest.mock import AsyncMock, patch

from app.modules.home.home_service import HomeService
from app.services.redis_api.redis_api_client import RedisApi


class TestHomeService:
    """Test cases for HomeService class."""
    
    @pytest.fixture
    def mock_redis_client(self):
        """Mock Redis client for testing."""
        return AsyncMock(spec=RedisApi)
    
    def test_home_service_init_with_redis_client(self, mock_redis_client):
        """Test HomeService initialization with provided Redis client."""
        service = HomeService(redis_client=mock_redis_client)
        assert service.redis == mock_redis_client
    
    def test_home_service_init_without_redis_client(self):
        """Test HomeService initialization without Redis client creates default."""
        with patch('app.modules.home.home_service.RedisApi') as mock_redis_class:
            mock_instance = AsyncMock()
            mock_redis_class.return_value = mock_instance
            
            service = HomeService()
            assert service.redis == mock_instance
            mock_redis_class.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_owners_with_vessel_success(self, mock_redis_client):
        """Test owners_with_vessel method with successful response."""
        # Setup
        service = HomeService(redis_client=mock_redis_client)
        expected_result = {
            "owners": [
                {
                    "vat": "NO950700939",
                    "name": "Test Owner",
                    "vessels": [
                        {"imo": "8918461", "name": "Wilson Saga"}
                    ]
                }
            ]
        }
        mock_redis_client.call.return_value = expected_result
        
        # Execute
        result = await service.owners_with_vessel(owner_vat="NO950700939")
        
        # Assert
        assert result == expected_result
        mock_redis_client.call.assert_called_once_with(
            path="get_owner_fleet_data",
            owner_vat="NO950700939"
        )
    
    @pytest.mark.asyncio
    async def test_owners_with_vessel_with_multiple_params(self, mock_redis_client):
        """Test owners_with_vessel method with multiple parameters."""
        # Setup
        service = HomeService(redis_client=mock_redis_client)
        expected_result = {"owners": []}
        mock_redis_client.call.return_value = expected_result
        
        params = {
            "owner_vat": "NO950700939",
            "vessel_type": "cargo",
            "active_only": True
        }
        
        # Execute
        result = await service.owners_with_vessel(**params)
        
        # Assert
        assert result == expected_result
        mock_redis_client.call.assert_called_once_with(
            path="get_owner_fleet_data",
            **params
        )
    
    @pytest.mark.asyncio
    async def test_owners_with_vessel_empty_result(self, mock_redis_client):
        """Test owners_with_vessel method with empty result."""
        # Setup
        service = HomeService(redis_client=mock_redis_client)
        mock_redis_client.call.return_value = {"owners": []}
        
        # Execute
        result = await service.owners_with_vessel(owner_vat="NO950700939")
        
        # Assert
        assert result == {"owners": []}
    
    @pytest.mark.asyncio
    async def test_owners_with_vessel_redis_exception(self, mock_redis_client):
        """Test owners_with_vessel method when Redis raises exception."""
        # Setup
        service = HomeService(redis_client=mock_redis_client)
        mock_redis_client.call.side_effect = Exception("Redis connection failed")
        
        # Execute & Assert
        with pytest.raises(Exception, match="Redis connection failed"):
            await service.owners_with_vessel(owner_vat="NO950700939")
    
    @pytest.mark.asyncio
    async def test_home_success(self, mock_redis_client):
        """Test home method with successful response."""
        # Setup
        service = HomeService(redis_client=mock_redis_client)
        payload = {
            "selected_owner_vat": "NO950700939",
            "vessel_imo": "8918461",
            "from_date": "2024-01-01 00:00:00",
            "to_date": "2024-01-07 23:59:59"
        }
        expected_result = {
            "home_page_data": {
                "vessel_info": {"name": "Wilson Saga"},
                "measurements": [],
                "summary": {}
            }
        }
        mock_redis_client.call.return_value = expected_result
        
        # Execute
        result = await service.home(payload)
        
        # Assert
        assert result == expected_result
        mock_redis_client.call.assert_called_once_with(
            path="home",
            key="home_page_data",
            method="POST",
            params=None,
            json=payload
        )
    
    @pytest.mark.asyncio
    async def test_home_with_complex_payload(self, mock_redis_client):
        """Test home method with complex payload structure."""
        # Setup
        service = HomeService(redis_client=mock_redis_client)
        payload = {
            "selected_owner_vat": "NO950700939",
            "vessel_imo": "8918461",
            "vessel_id": 32,
            "vessel_name": "Wilson Saga",
            "from_date": "2024-01-01 00:00:00",
            "to_date": "2024-01-07 23:59:59",
            "filters": {
                "include_measurements": True,
                "include_summary": True
            }
        }
        expected_result = {"home_page_data": {"complex": "data"}}
        mock_redis_client.call.return_value = expected_result
        
        # Execute
        result = await service.home(payload)
        
        # Assert
        assert result == expected_result
        mock_redis_client.call.assert_called_once_with(
            path="home",
            key="home_page_data",
            method="POST",
            params=None,
            json=payload
        )
    
    @pytest.mark.asyncio
    async def test_home_empty_payload(self, mock_redis_client):
        """Test home method with empty payload."""
        # Setup
        service = HomeService(redis_client=mock_redis_client)
        payload = {}
        expected_result = {"home_page_data": None}
        mock_redis_client.call.return_value = expected_result
        
        # Execute
        result = await service.home(payload)
        
        # Assert
        assert result == expected_result
        mock_redis_client.call.assert_called_once_with(
            path="home",
            key="home_page_data",
            method="POST",
            params=None,
            json=payload
        )
    
    @pytest.mark.asyncio
    async def test_home_redis_exception(self, mock_redis_client):
        """Test home method when Redis raises exception."""
        # Setup
        service = HomeService(redis_client=mock_redis_client)
        payload = {"selected_owner_vat": "NO950700939"}
        mock_redis_client.call.side_effect = Exception("Redis timeout")
        
        # Execute & Assert
        with pytest.raises(Exception, match="Redis timeout"):
            await service.home(payload)
    
    @pytest.mark.asyncio
    async def test_home_none_payload(self, mock_redis_client):
        """Test home method with None payload."""
        # Setup
        service = HomeService(redis_client=mock_redis_client)
        expected_result = {"home_page_data": None}
        mock_redis_client.call.return_value = expected_result
        
        # Execute
        result = await service.home(None)
        
        # Assert
        assert result == expected_result
        mock_redis_client.call.assert_called_once_with(
            path="home",
            key="home_page_data",
            method="POST",
            params=None,
            json=None
        )


class TestHomeServiceIntegration:
    """Integration tests for HomeService."""
    
    @pytest.mark.asyncio
    async def test_service_with_real_redis_client_mock(self):
        """Test HomeService with realistic Redis client behavior."""
        with patch('app.modules.home.home_service.RedisApi') as mock_redis_class:
            mock_client = AsyncMock()
            mock_redis_class.return_value = mock_client
            
            # Setup realistic responses
            mock_client.call.side_effect = [
                {"owners": [{"vat": "NO950700939", "vessels": []}]},  # owners_with_vessel
                {"home_page_data": {"vessel_info": {}}}  # home
            ]
            
            service = HomeService()
            
            # Test owners_with_vessel
            owners_result = await service.owners_with_vessel(owner_vat="NO950700939")
            assert "owners" in owners_result
            
            # Test home
            home_result = await service.home({"selected_owner_vat": "NO950700939"})
            assert "home_page_data" in home_result
            
            # Verify Redis client was called correctly
            assert mock_client.call.call_count == 2
    
    @pytest.mark.asyncio
    async def test_service_error_handling(self, mock_redis_client):
        """Test HomeService error handling with various Redis errors."""
        service = HomeService(redis_client=mock_redis_client)
        
        # Test different error scenarios
        error_scenarios = [
            ConnectionError("Connection lost"),
            TimeoutError("Request timeout"),
            ValueError("Invalid response format"),
            Exception("Generic error")
        ]
        
        for error in error_scenarios:
            mock_redis_client.call.side_effect = error
            
            with pytest.raises(type(error)):
                await service.owners_with_vessel(owner_vat="NO950700939")
            
            with pytest.raises(type(error)):
                await service.home({"selected_owner_vat": "NO950700939"})
    
    def test_service_singleton_behavior(self):
        """Test that HomeService doesn't enforce singleton pattern."""
        with patch('app.modules.home.home_service.RedisApi') as mock_redis_class:
            mock_redis_class.return_value = AsyncMock()
            
            service1 = HomeService()
            service2 = HomeService()
            
            # Services should be different instances
            assert service1 is not service2
            
            # But should use the same Redis client class
            assert mock_redis_class.call_count == 2
