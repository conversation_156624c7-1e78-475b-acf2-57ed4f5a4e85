# Home Logic - handles business logic for home dashboard operations

from app.general_logic_helper.controllers.data_controller import DataController
from app.general_logic_helper.handlers.measurements import Measurements
from app.modules.home.home_service import HomeService
from app.context.utils import rename_vat, select_keys


class HomeLogic:
    # Logic layer for home dashboard operations

    def __init__(self, home_service: HomeService, data_controller: DataController, measurements: Measurements):
        self.home_service = home_service
        self.data_controller = data_controller
        self.measurements = measurements

    async def owners_with_vessel(self, owner_vat: str):
        # Get owners with their associated vessels
        return await self.home_service.owners_with_vessel(**select_keys(locals()))

    async def home(self,
                   from_date: str,
                   owner_vat: str,
                   to_date: str,
                   vessel_id: int,
                   vessel_imo: str,
                   vessel_name: str):
        # Process home dashboard request - get data, add measurements, serve via WebSocket
        payload = rename_vat(**locals())
        data = await self.home_service.home(payload)
        if data is None:
            return None
        await self.measurements.get_and_set_measurements(payload, data)
        return await self.data_controller.wss_serve(data, payload)
