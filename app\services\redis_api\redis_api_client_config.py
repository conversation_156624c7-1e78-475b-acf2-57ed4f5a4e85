# Import required libraries
import os  # For accessing environment variables
from dotenv import load_dotenv  # For loading environment variables from .env file

from app.utils.singleton import Singleton

# Load environment variables from .env file into os.environ
load_dotenv()


class RedisClientConfig(metaclass=Singleton):
    """
    Simple settings container that reads from environment variables.
    Provides configuration settings for the Redis API client including:
    - API URL
    - Authentication token
    - Request timeout
    """
    def __init__(self):
        # Get Redis API URL from environment variable
        # This should be the base URL for the Redis API service
        self.redis_api_url = os.getenv("REDIS_API_URL")

        # Get authentication token from environment variable
        # This token will be used for Bearer authentication in API requests
        self.redis_api_token = os.getenv("REDIS_API_TOKEN")

        # Get timeout value from environment variable with a default of "600" seconds
        # This determines how long to wait for API responses before timing out
        timeout = os.getenv("REDIS_TIMEOUT", "600")  # TODO change this to what we need
        
        try:
            # Convert timeout string to integer
            self.redis_timeout = int(timeout)
        except ValueError:
            # If timeout value cannot be converted to integer,
            # fall back to default timeout of 600 seconds (10 minutes)
            self.redis_timeout = 600

        # Example of how to add additional configuration options:
        # self.redis_max_connections = int(os.getenv("REDIS_MAX_CONN", "10"))


# Create a singleton instance of the configuration
# This will be imported and used by the Redis API client
redis_client_config = RedisClientConfig()
