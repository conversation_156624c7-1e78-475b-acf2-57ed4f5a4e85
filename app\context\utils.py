from typing import Dict, Any, Callable, Optional, List, Union


def route_input(
    *,  # Force keyword-only arguments
    include_body: bool = False,
    headers_to_include: Optional[Union[List[str], Dict[str, str]]] = None
) -> Callable:
    """
    Decorator that configures how route parameters should be gathered from the request.
    Allows including request body and specific headers in the route parameters.
    
    Args:
        include_body: Whether to include the full request body in params
        headers_to_include: Headers to extract, either as list or mapping dict
    """
    def decorator(fn: Callable) -> Callable:
        # Attach configuration flags to the route function
        setattr(fn, "include_body", include_body)
        setattr(fn, "headers_to_include", headers_to_include or [])
        return fn
    return decorator


def rename_vat(*args: Any, **kwargs: Any) -> Dict[str, Any]:
    """
    Utility to handle parameter dictionaries that contain owner VAT keys.
    Can accept either a dict as first argument or keyword arguments.
    
    Two usage patterns:
    1. rename_vat(locals(), 'vessel_imo', 'owner_vat') - Dict with specific keys
    2. rename_vat(**locals()) - All local variables as kwargs
    """
    # Get source dict from args or kwargs
    source = next((a for a in args if isinstance(a, dict)), None)
    source = kwargs.copy() if source is None else source.copy()

    # Remove 'self' if present
    source.pop('self', None)

    # Get list of keys to keep (if provided as string args)
    names = [a for a in args if isinstance(a, str)]

    # Select keys and rename owner_vat
    picked = select_keys(source, *names)
    return rename_owner_vat(picked)


def select_keys(source: Dict[str, Any], *names: str) -> Dict[str, Any]:
    """
    Creates a new dict with only specified keys from source dict.
    If no keys specified, copies all except 'self'.
    
    Args:
        source: Source dictionary
        names: Optional keys to select
    """
    if not names:
        result = dict(source)  # Copy all
    else:
        result = {k: source[k] for k in names if k in source}  # Copy selected
    result.pop('self', None)  # Always remove 'self'
    return result


def rename_owner_vat(params: Dict[str, Any]) -> Dict[str, Any]:
    """
    Standardizes owner VAT parameter naming by converting 'owner_vat' 
    to 'selected_owner_vat' in the dictionary.
    
    Args:
        params: Dictionary potentially containing owner_vat key
    """
    return {
        ("selected_owner_vat" if k == "owner_vat" else k): v
        for k, v in params.items()
    }


def extract_kwargs(local_vars: dict) -> dict:
    """
    Creates a new dict from locals() without the 'self' parameter.
    Used to extract function parameters for passing to other functions.
    
    Args:
        local_vars: Usually the locals() dict
    """
    return {k: v for k, v in local_vars.items() if k != "self"}
