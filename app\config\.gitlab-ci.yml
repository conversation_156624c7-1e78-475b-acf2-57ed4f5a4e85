#image: gitlab.frugaltech.lan:5050/docker-images/images/backend
image: python:3.11-slim

before_script:
  - apt-get update
  - DEBIAN_FRONTEND=noninteractive TZ=Etc/UTC apt-get -y install gcc mono-mcs
  - apt-get -y install libmariadb3 libmariadb-dev

stages:
  - test
  - merge
  - build
  - deploy

test-job:
  stage: test

  script:
    - 'echo running test'
    # Fetches the latest changes from origin for the target branch. 
    # In this case the target branch would either be main or release
    #- git fetch origin $CI_MERGE_REQUEST_TARGET_BRANCH_NAME
    # Checkouts a new local branch by using the origin target branch (main or release)
    #- git checkout -B $CI_MERGE_REQUEST_TARGET_BRANCH_NAME origin/$CI_MERGE_REQUEST_TARGET_BRANCH_NAME 
    # The variable CI_COMMIT_SHA points to the exact commit that triggerd the test-job. 
    # Those changes are merged into the newly created local main/release branch
    #- git merge $CI_COMMIT_SHA
    # Installs dependencies and runs test
    #- python3 -m venv venv
    #- source venv/bin/activate
    #- pip install -r requirements.txt
    #- cd tests
    #- 'echo no tests to be run'
    #- pytest test_ci.py # TODO needs to be changed to run all the defined tests
  rules:
    # The variable CI_PIPELINE_SOURCE specifices what kind of event can trigger this job. 
    # In this case merge_request_events can trigger this job and the merge request target branch has
    # either to be main or release.
    - if: '$CI_PIPELINE_SOURCE == "merge_request_event" && ($CI_MERGE_REQUEST_TARGET_BRANCH_NAME == "main" || $CI_MERGE_REQUEST_TARGET_BRANCH_NAME == "release")'

build-job-test:
  stage: build
  script:
    - 'echo building build/distribution for backend test'
    # Fetches the origin main branch
    #- git fetch origin $CI_DEFAULT_BRANCH
    #- git checkout -B $CI_DEFAULT_BRANCH origin/$CI_DEFAULT_BRANCH
    #- git archive --format=zip main -o frugaltech-mydata-be.zip
  rules:
    # Determines the name of the branch associated with the commit that can trigger this job.
    #- if: '$CI_COMMIT_BRANCH == "main"'
  #artifacts:
    # Saves the build as an artifact that can be used in other stages.
    # The expire_in determines how long GitLab keeps the artifact.
    #paths:
      #- frugaltech-mydata-be.zip
    #expire_in: 1 day

build-job-prod:
  stage: build
  script:
    - 'echo building build/distribution for backend production'
    # Fetches the origin main branch
    #- git fetch origin release
    #- git checkout -B release origin/release
    #- git archive --format=zip release -o frugaltech-mydata-be.zip
  rules:
    # Determines the name of the branch associated with the commit that can trigger this job.
    #- if: '$CI_COMMIT_BRANCH == "release"'
  #artifacts:
    # Saves the build as an artifact that can be used in other stages.
    # The expire_in determines how long GitLab keeps the artifact.
    #paths:
      #- frugaltech-mydata-be.zip
    #expire_in: 1 day

deploy-test-job:
  stage: deploy
  before_script:
    - echo "Matthew says Hi"
    #- apt-get update -qq # Updates packages in quite mode (suppresses output)
    #- apt-get install -qq git # Installs git in quite mode
    #- 'which ssh-agent || ( apt-get install -qq openssh-client )' # Checks if ssh-agent is available if not then it installs OpenSSH client
    #- eval $(ssh-agent -s) # Starts the SSH Agent
    #- ssh-add <(echo "$gitlab_mydata_be") # Adds the SSH key to the SSH agent, the key  is stored as an environment variable and can be found in admin area settings -> CI/CD -> variables 
    #- mkdir -p ~/.ssh # Creates an .ssh folder
    # Checks if /.dockerenv file exists which indicates that the job is running within a Docker container. 
    # If it does, it appends SSH configuration settings to the ~/.ssh/config file to disable strict host key checking. 
    # This can be useful when running SSH commands in a Docker container where interactive host key verification is not possible
    #- '[[ -f /.dockerenv ]] && echo -e "Host *\n\tStrictHostKeyChecking no\n\n" > ~/.ssh/config'

  script:
    - 'echo deploying to test environment'
    # Secure copies everything from build stage (artifact) and uploads it to /home/<USER>/download
    #- ssh <EMAIL> "sudo /bin/rm /home/<USER>/download/frugaltech-mydata-be.zip"
    #- scp -r frugaltech-mydata-be.zip <EMAIL>:/home/<USER>/download
    # Connects, logs into be.frugaltech.lan and executes the deploy scripts for INTERNAL test and PUBLIC test.
    # deploy-nextgen.sh is for the INTERNAL test environment and deploy-nextgen-test.sh is for the PUBLIC test environment
    #- ssh <EMAIL> "sudo /home/<USER>/deploy-nextgen-test.sh && sudo /home/<USER>/deploy-nextgen.sh"
  rules:
    - if: $CI_COMMIT_BRANCH == "main"

merge-job:
  stage: merge
  script:
    # If the commit message includes hotfix then this will run
    - 'echo merging release into main after hotfix'
    #- git fetch origin $CI_DEFAULT_BRANCH
    #- git checkout -B $CI_DEFAULT_BRANCH origin/$CI_DEFAULT_BRANCH
    #- git fetch origin $CI_COMMIT_REF_NAME
    #- git checkout -B $CI_COMMIT_REF_NAME origin/$CI_COMMIT_REF_NAME
    #- git checkout $CI_DEFAULT_BRANCH 
    #- git merge $CI_COMMIT_REF_NAME --allow-unrelated-histories
    #- git push -o $CI_DEFAULT_BRANCH http://__token__:**************************@$CI_SERVER_HOST/$CI_PROJECT_PATH.git 
  rules:
    #- if: '$CI_PIPELINE_SOURCE == "push" && $CI_COMMIT_REF_NAME == "release" && $CI_COMMIT_MESSAGE =~ /hotfix/i'

deploy-prod-job:
  stage: deploy
  #before_script:
    #- apt-get update -qq # Updates packages in quite mode (suppresses output)
    #- apt-get install -qq git # Installs git in quite mode
    #- 'which ssh-agent || ( apt-get install -qq openssh-client )' # Checks if ssh-agent is available if not then it installs OpenSSH client
    #- eval $(ssh-agent -s) # Starts the SSH Agent
    #- ssh-add <(echo "gitlab_mydata_be") # Adds the SSH key to the SSH agent, the key  is stored as an environment variable and can be found in settings -> CI/CD -> Variables
    #- mkdir -p ~/.ssh # Creates an .ssh folder
    # Checks if /.dockerenv file exists which indicates that the job is running within a Docker container. 
    # If it does, it appends SSH configuration settings to the ~/.ssh/config file to disable strict host key checking. 
    # This can be useful when running SSH commands in a Docker container where interactive host key verification is not possible
    #- '[[ -f /.dockerenv ]] && echo -e "Host *\n\tStrictHostKeyChecking no\n\n" > ~/.ssh/config'

  script:
    - 'echo the build/distribution can be found under the projects jobs look for the job "deploy-prod-job". Now manually deploy this build/distribution'
    #- 'echo deploying to test environment'
    # Secure copies everything from build stage (artifact) and uploads it to /home/<USER>/download
    #- ssh <EMAIL> "sudo /bin/rm /home/<USER>/download/frugaltech-mydata-be.zip"
    #- scp -r frugaltech-mydata-be.zip <EMAIL>:/home/<USER>/download
    # Connects, logs into be.frugaltech.lan and executes the deploy scripts for INTERNAL test and PUBLIC test.
    # deploy-nextgen.sh is for the INTERNAL test environment and deploy-nextgen-test.sh is for the PUBLIC test environment
    #- ssh <EMAIL> "sudo /home/<USER>/deploy-nextgen-test.sh && sudo /home/<USER>/deploy-nextgen.sh"
  rules:
    - if: $CI_COMMIT_BRANCH == "release"
    # Specifies that this stage requires manual interaction before it will run.
    #- when: manual  
