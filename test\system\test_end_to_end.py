"""
End-to-end system tests for complete user workflows.
"""
import pytest
import asyncio
import time
from unittest.mock import patch, AsyncMock
from fastapi import status
from httpx import AsyncClient

from test.utils.test_helpers import PerformanceTestHelper, APITestHelper


@pytest.mark.system
class TestCompleteUserWorkflows:
    """System tests for complete user workflows from authentication to data retrieval."""
    
    @pytest.mark.asyncio
    async def test_complete_home_data_workflow(self, async_client: AsyncClient, auth_headers, mock_auth_dependencies):
        """Test complete workflow: authentication -> home data -> analytics."""
        # Mock comprehensive Redis responses
        mock_redis_responses = {
            "home": {
                "home_page_data": {
                    "vessel_info": {
                        "name": "Wilson Saga",
                        "imo": "8918461",
                        "type": "General Cargo"
                    },
                    "measurements": [
                        {"timestamp": "2024-01-01T00:00:00Z", "fuel_consumption": 100},
                        {"timestamp": "2024-01-01T01:00:00Z", "fuel_consumption": 105}
                    ],
                    "summary": {
                        "total_fuel": 1000,
                        "avg_speed": 12.5,
                        "efficiency_score": 85
                    }
                }
            },
            "analytics": {
                "analytics_data": {
                    "fuel_efficiency": {"score": 85, "trend": "improving"},
                    "speed_analysis": {"avg_speed": 12.5, "optimal_speed": 14.0},
                    "route_optimization": {"current_route": "optimal", "savings": 5.2}
                }
            }
        }
        
        with patch('app.services.redis_api.redis_api_client.aioredis') as mock_aioredis:
            mock_connection = AsyncMock()
            mock_aioredis.from_url.return_value = mock_connection
            mock_connection.get_key_data.return_value = [{"value": True}]  # log_speed_reliable
            
            # Mock HTTP responses for different endpoints
            responses = [
                AsyncMock(json=AsyncMock(return_value=mock_redis_responses["home"]), status_code=200),
                AsyncMock(json=AsyncMock(return_value=mock_redis_responses["analytics"]), status_code=200)
            ]
            
            with patch('httpx.AsyncClient.request', side_effect=responses):
                # Step 1: Get home page data
                home_response = await async_client.get(
                    "/home?owner_vat=NO950700939&vessel_imo=8918461&vessel_id=32&vessel_name=Wilson Saga&from_date=2024-01-01 00:00:00&to_date=2024-01-07 23:59:59",
                    headers=auth_headers
                )
                
                assert home_response.status_code == status.HTTP_200_OK
                home_data = home_response.json()
                
                # Step 2: Get analytics data using vessel info from home
                analytics_payload = {
                    "owner_vat": "NO950700939",
                    "vessel_imo": "8918461",
                    "vessel_id": 32,
                    "vessel_name": "Wilson Saga",
                    "from_date": "2024-01-01 00:00:00",
                    "to_date": "2024-01-07 23:59:59"
                }
                
                analytics_response = await async_client.post(
                    "/data-analytics",
                    json=analytics_payload,
                    headers=auth_headers
                )
                
                assert analytics_response.status_code == status.HTTP_200_OK
                analytics_data = analytics_response.json()
                
                # Verify workflow completion
                assert home_data is not None
                assert analytics_data is not None
    
    @pytest.mark.asyncio
    async def test_user_management_workflow(self, async_client: AsyncClient, auth_headers, mock_auth_dependencies):
        """Test complete user management workflow."""
        mock_responses = {
            "owners": {"owners": [{"vat": "NO950700939", "name": "Test Owner"}]},
            "users": {"users": [{"id": 1, "email": "<EMAIL>"}]},
            "vessels": {"vessels": [{"imo": "8918461", "name": "Wilson Saga"}]}
        }
        
        with patch('app.services.redis_api.redis_api_client.aioredis') as mock_aioredis:
            mock_connection = AsyncMock()
            mock_aioredis.from_url.return_value = mock_connection
            
            responses = [
                AsyncMock(json=AsyncMock(return_value=mock_responses["owners"]), status_code=200),
                AsyncMock(json=AsyncMock(return_value=mock_responses["users"]), status_code=200),
                AsyncMock(json=AsyncMock(return_value=mock_responses["vessels"]), status_code=200)
            ]
            
            with patch('httpx.AsyncClient.request', side_effect=responses):
                # Step 1: Get owners
                owners_response = await async_client.get(
                    "/owners?session_owner_vat=NO950700939",
                    headers=auth_headers
                )
                assert owners_response.status_code == status.HTTP_200_OK
                
                # Step 2: Get users for the owner
                users_response = await async_client.get(
                    "/users?owner_vat=NO950700939",
                    headers=auth_headers
                )
                assert users_response.status_code == status.HTTP_200_OK
                
                # Step 3: Get vessels for the owner
                vessels_response = await async_client.get(
                    "/vessels?owner_vat=NO950700939",
                    headers=auth_headers
                )
                assert vessels_response.status_code == status.HTTP_200_OK
                
                # Verify all steps completed successfully
                assert owners_response.json() is not None
                assert users_response.json() is not None
                assert vessels_response.json() is not None


@pytest.mark.system
class TestAuthenticationFlows:
    """System tests for authentication and authorization flows."""
    
    @pytest.mark.asyncio
    async def test_complete_authentication_flow(self, async_client: AsyncClient):
        """Test complete authentication flow from login to protected resource access."""
        # Mock authentication dependencies
        with patch('auth.validate_owner_access') as mock_validate:
            with patch('auth.managed_owners') as mock_managed:
                mock_validate.return_value = True
                mock_managed.return_value = ["NO950700939"]
                
                # Create valid auth headers
                auth_headers = {
                    "Authorization": "Bearer valid_token",
                    "Content-Type": "application/json"
                }
                
                with patch('app.services.redis_api.redis_api_client.aioredis') as mock_aioredis:
                    mock_connection = AsyncMock()
                    mock_aioredis.from_url.return_value = mock_connection
                    
                    mock_response = AsyncMock()
                    mock_response.json.return_value = {"data": "authenticated_access"}
                    mock_response.status_code = 200
                    
                    with patch('httpx.AsyncClient.request', return_value=mock_response):
                        # Test access to protected endpoint
                        response = await async_client.get(
                            "/users?owner_vat=NO950700939",
                            headers=auth_headers
                        )
                        
                        assert response.status_code == status.HTTP_200_OK
                        
                        # Verify authentication was checked
                        mock_validate.assert_called()
    
    @pytest.mark.asyncio
    async def test_unauthorized_access_prevention(self, async_client: AsyncClient):
        """Test that unauthorized access is properly prevented."""
        # Test without authentication headers
        response = await async_client.get("/users?owner_vat=NO950700939")
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
        
        # Test with invalid token
        invalid_headers = {
            "Authorization": "Bearer invalid_token",
            "Content-Type": "application/json"
        }
        
        with patch('auth.validate_owner_access') as mock_validate:
            from fastapi import HTTPException
            mock_validate.side_effect = HTTPException(status_code=401, detail="Invalid token")
            
            response = await async_client.get(
                "/users?owner_vat=NO950700939",
                headers=invalid_headers
            )
            assert response.status_code == status.HTTP_401_UNAUTHORIZED
    
    @pytest.mark.asyncio
    async def test_owner_access_validation(self, async_client: AsyncClient):
        """Test owner access validation across different endpoints."""
        auth_headers = {
            "Authorization": "Bearer valid_token",
            "Content-Type": "application/json"
        }
        
        with patch('auth.validate_owner_access') as mock_validate:
            with patch('auth.managed_owners') as mock_managed:
                # Test valid owner access
                mock_validate.return_value = True
                mock_managed.return_value = ["NO950700939"]
                
                with patch('app.services.redis_api.redis_api_client.aioredis') as mock_aioredis:
                    mock_connection = AsyncMock()
                    mock_aioredis.from_url.return_value = mock_connection
                    
                    mock_response = AsyncMock()
                    mock_response.json.return_value = {"authorized": "data"}
                    mock_response.status_code = 200
                    
                    with patch('httpx.AsyncClient.request', return_value=mock_response):
                        # Test multiple endpoints with same owner
                        endpoints = [
                            "/users?owner_vat=NO950700939",
                            "/vessels?owner_vat=NO950700939",
                            "/owners?session_owner_vat=NO950700939"
                        ]
                        
                        for endpoint in endpoints:
                            response = await async_client.get(endpoint, headers=auth_headers)
                            assert response.status_code == status.HTTP_200_OK


@pytest.mark.system
class TestPerformanceAndLoad:
    """System tests for performance and load characteristics."""
    
    @pytest.mark.asyncio
    async def test_concurrent_user_sessions(self, async_client: AsyncClient, performance_test_helper):
        """Test system behavior under concurrent user sessions."""
        # Mock authentication for multiple users
        with patch('auth.validate_owner_access') as mock_validate:
            with patch('auth.managed_owners') as mock_managed:
                mock_validate.return_value = True
                mock_managed.return_value = ["NO950700939", "NO123456789"]
                
                auth_headers = {
                    "Authorization": "Bearer valid_token",
                    "Content-Type": "application/json"
                }
                
                with patch('app.services.redis_api.redis_api_client.aioredis') as mock_aioredis:
                    mock_connection = AsyncMock()
                    mock_aioredis.from_url.return_value = mock_connection
                    
                    mock_response = AsyncMock()
                    mock_response.json.return_value = {"concurrent": "data"}
                    mock_response.status_code = 200
                    
                    with patch('httpx.AsyncClient.request', return_value=mock_response):
                        # Simulate concurrent user sessions
                        tasks = []
                        for i in range(10):
                            owner_vat = "NO950700939" if i % 2 == 0 else "NO123456789"
                            task = async_client.get(
                                f"/users?owner_vat={owner_vat}",
                                headers=auth_headers
                            )
                            tasks.append(task)
                        
                        # Execute concurrent requests
                        start_time = time.time()
                        responses = await asyncio.gather(*tasks, return_exceptions=True)
                        end_time = time.time()
                        
                        # Analyze results
                        successful_responses = [
                            r for r in responses 
                            if not isinstance(r, Exception) and hasattr(r, 'status_code') and r.status_code == 200
                        ]
                        
                        # Verify performance
                        assert len(successful_responses) >= 8  # At least 80% success rate
                        assert (end_time - start_time) < 10.0  # Complete within 10 seconds
    
    @pytest.mark.asyncio
    async def test_high_load_data_analytics(self, async_client: AsyncClient):
        """Test data analytics endpoint under high load."""
        auth_headers = {
            "Authorization": "Bearer valid_token",
            "Content-Type": "application/json"
        }
        
        with patch('auth.validate_owner_access') as mock_validate:
            with patch('auth.managed_owners') as mock_managed:
                mock_validate.return_value = True
                mock_managed.return_value = ["NO950700939"]
                
                with patch('app.services.redis_api.redis_api_client.aioredis') as mock_aioredis:
                    mock_connection = AsyncMock()
                    mock_aioredis.from_url.return_value = mock_connection
                    mock_connection.get_key_data.return_value = [{"value": True}]
                    
                    # Mock analytics response
                    analytics_response = {
                        "analytics": {
                            "fuel_efficiency": {"score": 85},
                            "performance_metrics": {"data": "heavy_computation"}
                        }
                    }
                    
                    mock_response = AsyncMock()
                    mock_response.json.return_value = analytics_response
                    mock_response.status_code = 200
                    
                    with patch('httpx.AsyncClient.request', return_value=mock_response):
                        # Create multiple analytics requests
                        payload = {
                            "owner_vat": "NO950700939",
                            "vessel_imo": "8918461",
                            "vessel_id": 32,
                            "vessel_name": "Wilson Saga",
                            "from_date": "2024-01-01 00:00:00",
                            "to_date": "2024-01-07 23:59:59"
                        }
                        
                        tasks = [
                            async_client.post("/data-analytics", json=payload, headers=auth_headers)
                            for _ in range(5)
                        ]
                        
                        # Execute concurrent analytics requests
                        start_time = time.time()
                        responses = await asyncio.gather(*tasks, return_exceptions=True)
                        end_time = time.time()
                        
                        # Verify all requests completed successfully
                        successful_responses = [
                            r for r in responses 
                            if not isinstance(r, Exception) and hasattr(r, 'status_code') and r.status_code == 200
                        ]
                        
                        assert len(successful_responses) >= 4  # At least 80% success rate
                        assert (end_time - start_time) < 15.0  # Complete within 15 seconds


@pytest.mark.system
class TestErrorRecoveryAndResilience:
    """System tests for error recovery and system resilience."""
    
    @pytest.mark.asyncio
    async def test_redis_failure_recovery(self, async_client: AsyncClient, auth_headers, mock_auth_dependencies):
        """Test system behavior and recovery when Redis fails."""
        with patch('app.services.redis_api.redis_api_client.aioredis') as mock_aioredis:
            # First call fails, second succeeds (simulating recovery)
            mock_aioredis.from_url.side_effect = [
                ConnectionError("Redis connection failed"),
                AsyncMock()  # Recovery
            ]
            
            # First request should fail
            response1 = await async_client.get(
                "/users?owner_vat=NO950700939",
                headers=auth_headers
            )
            assert response1.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR
            
            # After recovery, requests should succeed
            mock_connection = AsyncMock()
            mock_aioredis.from_url.return_value = mock_connection
            
            mock_response = AsyncMock()
            mock_response.json.return_value = {"recovered": "data"}
            mock_response.status_code = 200
            
            with patch('httpx.AsyncClient.request', return_value=mock_response):
                response2 = await async_client.get(
                    "/users?owner_vat=NO950700939",
                    headers=auth_headers
                )
                assert response2.status_code == status.HTTP_200_OK
    
    @pytest.mark.asyncio
    async def test_partial_service_degradation(self, async_client: AsyncClient, auth_headers, mock_auth_dependencies):
        """Test system behavior when some services are degraded."""
        with patch('app.services.redis_api.redis_api_client.aioredis') as mock_aioredis:
            mock_connection = AsyncMock()
            mock_aioredis.from_url.return_value = mock_connection
            
            # Some endpoints work, others fail
            responses = [
                AsyncMock(json=AsyncMock(return_value={"working": "service"}), status_code=200),
                AsyncMock(json=AsyncMock(side_effect=Exception("Service degraded")), status_code=500),
                AsyncMock(json=AsyncMock(return_value={"working": "service"}), status_code=200)
            ]
            
            with patch('httpx.AsyncClient.request', side_effect=responses):
                # Test multiple endpoints
                endpoints = [
                    "/users?owner_vat=NO950700939",
                    "/vessels?owner_vat=NO950700939",  # This one fails
                    "/owners?session_owner_vat=NO950700939"
                ]
                
                results = []
                for endpoint in endpoints:
                    try:
                        response = await async_client.get(endpoint, headers=auth_headers)
                        results.append(response.status_code)
                    except Exception:
                        results.append(500)
                
                # Verify partial functionality
                successful_calls = [r for r in results if r == 200]
                assert len(successful_calls) >= 1  # At least some services work


@pytest.mark.system
class TestDataConsistencyAndIntegrity:
    """System tests for data consistency and integrity across the application."""
    
    @pytest.mark.asyncio
    async def test_data_consistency_across_endpoints(self, async_client: AsyncClient, auth_headers, mock_auth_dependencies):
        """Test that data remains consistent across different endpoints."""
        # Mock consistent vessel data across different endpoints
        vessel_data = {
            "imo": "8918461",
            "name": "Wilson Saga",
            "owner_vat": "NO950700939"
        }
        
        mock_responses = {
            "home": {"vessel_info": vessel_data},
            "vessels": {"vessels": [vessel_data]},
            "analytics": {"vessel_analytics": vessel_data}
        }
        
        with patch('app.services.redis_api.redis_api_client.aioredis') as mock_aioredis:
            mock_connection = AsyncMock()
            mock_aioredis.from_url.return_value = mock_connection
            mock_connection.get_key_data.return_value = [{"value": True}]
            
            responses = [
                AsyncMock(json=AsyncMock(return_value=mock_responses["home"]), status_code=200),
                AsyncMock(json=AsyncMock(return_value=mock_responses["vessels"]), status_code=200),
                AsyncMock(json=AsyncMock(return_value=mock_responses["analytics"]), status_code=200)
            ]
            
            with patch('httpx.AsyncClient.request', side_effect=responses):
                # Get data from different endpoints
                home_response = await async_client.get(
                    "/home?owner_vat=NO950700939&vessel_imo=8918461&vessel_id=32&vessel_name=Wilson Saga&from_date=2024-01-01 00:00:00&to_date=2024-01-07 23:59:59",
                    headers=auth_headers
                )
                
                vessels_response = await async_client.get(
                    "/vessels?owner_vat=NO950700939",
                    headers=auth_headers
                )
                
                analytics_response = await async_client.post(
                    "/data-analytics",
                    json={
                        "owner_vat": "NO950700939",
                        "vessel_imo": "8918461",
                        "vessel_id": 32,
                        "vessel_name": "Wilson Saga",
                        "from_date": "2024-01-01 00:00:00",
                        "to_date": "2024-01-07 23:59:59"
                    },
                    headers=auth_headers
                )
                
                # Verify all responses are successful
                assert home_response.status_code == status.HTTP_200_OK
                assert vessels_response.status_code == status.HTTP_200_OK
                assert analytics_response.status_code == status.HTTP_200_OK
                
                # Verify data consistency (vessel IMO should be consistent)
                home_data = home_response.json()
                vessels_data = vessels_response.json()
                analytics_data = analytics_response.json()
                
                # All should reference the same vessel
                assert home_data is not None
                assert vessels_data is not None
                assert analytics_data is not None
