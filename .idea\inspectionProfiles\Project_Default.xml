<component name="InspectionProjectProfileManager">
  <profile version="1.0">
    <option name="myName" value="Project Default" />
    <inspection_tool class="Eslint" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="PyPackageRequirementsInspection" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoredPackages">
        <value>
          <list size="29">
            <item index="0" class="java.lang.String" itemvalue="mariadb" />
            <item index="1" class="java.lang.String" itemvalue="pandas" />
            <item index="2" class="java.lang.String" itemvalue="Werkzeug" />
            <item index="3" class="java.lang.String" itemvalue="arrow" />
            <item index="4" class="java.lang.String" itemvalue="Flask" />
            <item index="5" class="java.lang.String" itemvalue="scipy" />
            <item index="6" class="java.lang.String" itemvalue="joblib" />
            <item index="7" class="java.lang.String" itemvalue="pip" />
            <item index="8" class="java.lang.String" itemvalue="numpy" />
            <item index="9" class="java.lang.String" itemvalue="frugaltech-utilities" />
            <item index="10" class="java.lang.String" itemvalue="contourpy" />
            <item index="11" class="java.lang.String" itemvalue="pymongo" />
            <item index="12" class="java.lang.String" itemvalue="certifi" />
            <item index="13" class="java.lang.String" itemvalue="orjson" />
            <item index="14" class="java.lang.String" itemvalue="packaging" />
            <item index="15" class="java.lang.String" itemvalue="pytz" />
            <item index="16" class="java.lang.String" itemvalue="requests" />
            <item index="17" class="java.lang.String" itemvalue="urllib3" />
            <item index="18" class="java.lang.String" itemvalue="redis" />
            <item index="19" class="java.lang.String" itemvalue="pytest" />
            <item index="20" class="java.lang.String" itemvalue="wheel" />
            <item index="21" class="java.lang.String" itemvalue="pillow" />
            <item index="22" class="java.lang.String" itemvalue="setuptools" />
            <item index="23" class="java.lang.String" itemvalue="plotly" />
            <item index="24" class="java.lang.String" itemvalue="mlflow" />
            <item index="25" class="java.lang.String" itemvalue="python-Levenshtein" />
            <item index="26" class="java.lang.String" itemvalue="sympy" />
            <item index="27" class="java.lang.String" itemvalue="flask-cors" />
            <item index="28" class="java.lang.String" itemvalue="gunicorn" />
          </list>
        </value>
      </option>
    </inspection_tool>
  </profile>
</component>