# Owner Router - API endpoints for owner management operations

from fastapi import APIRouter, Depends

from app.context.utils import route_input
from app.modules.owner.owner_logic import OwnerLogic
from app.modules.owner.owner_service import OwnerService
from app.context.context import get_context
from app.config.settings import settings

# Create router with owners tag
router = APIRouter(tags=["Owners"])

# Set up dependency injection for logic and service layers
get_owner_params, get_owner_logic = get_context(
    OwnerLogic,
    OwnerService,
)


@router.get("/owners")
@route_input(headers_to_include={settings.session_owner_vat: "session_owner_vat"})
async def owners(
    params: dict = Depends(get_owner_params),
    logic: OwnerLogic = Depends(get_owner_logic),
):
    # GET endpoint to retrieve owners data with session VAT header
    return await logic.owners(**params)
