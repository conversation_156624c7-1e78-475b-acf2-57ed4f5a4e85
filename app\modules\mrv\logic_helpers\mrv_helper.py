import numpy as np
from orjson import orjson
from io import BytesIO
from fpdf import FPDF
from dateutil import parser
from fastapi.responses import StreamingResponse


class MrvHelper:

    def __init__(self):
        self.fuel_types = ["hfo", "lfo", "mdo"]

    # Handle data and calculations
    def is_voyage_complete(self, v: dict) -> bool:
        """
        Require all standard fields AND at least one of the HFO/LFO/MDO consumptions > 0
        AND at least one of time_sailed_days/hours/minutes > 0.
        """

        if "voyage_type" not in v:
            return False

        if v["voyage_type"].lower() == "":
            return False

        # 1) Core required fields (no overall `fuel_consumption` any more,
        #    and we’ve pulled out the time_sailed_* fields for a separate check)
        required = [
            "departure_port", "destination_port",
            "start_time", "end_time",
            "distance", "cargo", "transport_work",
        ]
        for f in required:
            val = v.get(f)
            if val is None or (isinstance(val, str) and not val.strip()) or (
                    isinstance(val, (int, float)) and val == 0):
                return False

        # 2) At least one of the time_sailed fields must be > 0
        time_keys = ["time_sailed_days", "time_sailed_hours", "time_sailed_minutes"]
        has_time = any(
            isinstance(v.get(k), (int, float)) and v.get(k) > 0
            for k in time_keys
        )
        if not has_time:
            return False

        # 3) At least one specific fuel‐type consumption must be > 0
        fuel_keys = ["fuel_consumption_HFO", "fuel_consumption_LFO", "fuel_consumption_MDO"]
        has_fuel = any(
            isinstance(v.get(k), (int, float)) and v.get(k) > 0
            for k in fuel_keys
        )
        if not has_fuel:
            return False

        return True

    def has_voyage_period_overlap(self, v: dict, all_vs: list[dict]) -> bool:
        try:
            s1 = parser.isoparse(v["start_time"])
            e1 = parser.isoparse(v["end_time"])
        except Exception as e:
            print(e)
            return False

        for other in all_vs:
            if other['voyage_id'] == v['voyage_id']:
                continue
            try:
                s2 = parser.isoparse(other["start_time"])
                e2 = parser.isoparse(other["end_time"])
            except Exception as e:
                print(e)
                continue
            if s1 < e2 and s2 < e1:
                return True
        return False

    def annotate_voyage_statuses(self, voyages: dict[str, dict]) -> dict[str, dict]:
        all_vs = list(voyages.values())
        annotated: dict[str, dict] = {}

        for key, voyage in voyages.items():
            v = voyage.copy()
            is_complete = self.is_voyage_complete(v)
            status = "Complete" if is_complete else "Incomplete"

            if len(v.get("errors", [])) > 0:
                status = "Error"
            elif is_complete and v.get("approved_mrv_voyage", False):
                status = "Approved"

            # also treat any overlap as an error
            if status != "Error" and self.has_voyage_period_overlap(v, all_vs):
                status = "Error"

            v["voyage_status"] = status
            annotated[key] = v

        return annotated

    def exclude_non_mrv_voyages(self, mrv_data):
        return {
            key: voyage
            for key, voyage in mrv_data.items()
            if not (voyage.get('eu_heading') is None and voyage.get('uk_heading') is None)
        }

    def full_width_table_section(self, pdf: FPDF, section_title: str, data: dict,
                                 col1_ratio: float, col2_ratio: float):
        # -- Section heading, left-aligned --
        pdf.set_font("Arial", "B", 14)
        pdf.cell(0, 8, section_title, ln=True, align="L")
        pdf.ln(2)

        # -- Data rows, left-aligned, with borders (no Field/Value header) --
        pdf.set_font("Arial", "", 12)
        for key, val in data.items():
            pdf.cell(col1_ratio, 8, str(key or ""), border=1, align="L")
            pdf.cell(col2_ratio, 8, str(val or ""), border=1, align="L")
            pdf.ln()
        pdf.ln(4)

    def create_pdf_report(self, data: dict, vessel_name, vessel_imo):
        """
        Generate the PDF report, using consistent column ratios for all sections
        so that everything lines up horizontally.
        """
        pdf = FPDF(orientation='P', unit='mm', format='A4')
        pdf.set_margins(left=15, top=10, right=15)
        pdf.set_auto_page_break(auto=True, margin=15)
        pdf.add_page()

        # Main Title at the top — now left-aligned instead of centered
        pdf.set_font("Arial", "B", 16)
        pdf.cell(0, 10, "MRV Voyage Report", ln=True, align="C")  # <-- align changed
        pdf.ln(5)

        # 1) Vessel Information
        vessel_info = {
            "Name": vessel_name,
            "IMO": vessel_imo,
        }
        self.full_width_table_section(
            pdf, "Vessel Information", vessel_info,
            col1_ratio=50, col2_ratio=120
        )

        # 2) Voyage Information
        voyage_info = {
            "Departure": data.get("start_time", "N/A").replace("T", " ").split(".")[0],
            "Arrival": data.get("end_time", "N/A").replace("T", " ").split(".")[0],
            "Departure Port": data.get("departure_port", "N/A"),
            "Arrival Port": data.get("destination_port", "N/A"),
            "Time at Sea": f"{data.get('time_sailed_days', 0)}d {data.get('time_sailed_hours', 0)}h {data.get('time_sailed_minutes', 0)}m",
            "Distance": f"{data.get('distance', 'N/A')} nm" if data.get('distance') else "N/A",
            "Cargo": f"{data.get('cargo', 'N/A')} mt",
            "Transport Work": f"{data.get('transport_work', 'N/A')} mt/nm",
            "Voyage Type": data.get("voyage_type", "N/A"),
        }
        self.full_width_table_section(
            pdf, "Voyage Information", voyage_info,
            col1_ratio=50, col2_ratio=120
        )

        # 3) Fuel
        fuel_data = {}
        # Define fuel types mapping: key is the display name, value is a list of potential keys to check in order.
        fuel_keys = {
            "HFO": ["fuel_consumption_hfo", "fuel_consumption_HFO"],
            "MDO": ["fuel_consumption_mdo", "fuel_consumption_MDO"],
            "LFO": ["fuel_consumption_lfo", "fuel_consumption_LFO"]
        }

        total_fuel = 0.0
        # Process fuel consumption values and accumulate rounded values
        for fuel_type, keys in fuel_keys.items():
            fuel_value = next((data[k] for k in keys if data.get(k) is not None), None)
            if fuel_value is not None:
                try:
                    val = round(float(fuel_value), 2)
                    fuel_data[fuel_type] = f"{val} mt"
                    total_fuel += val
                except (ValueError, TypeError):
                    fuel_data[fuel_type] = f"{fuel_value} mt"

        if total_fuel > 0:
            fuel_data["Total"] = f"{round(total_fuel, 2)} mt"

        self.full_width_table_section(
            pdf, "Fuel Consumption", fuel_data,
            col1_ratio=50, col2_ratio=120
        )

        # 4) Emissions
        emissions_data = {}
        emissions_keys = {
            "HFO": ["emissions_hfo", "emissions_HFO"],
            "MDO": ["emissions_mdo", "emissions_MDO"],
            "LFO": ["emissions_lfo", "emissions_LFO"]
        }

        total_emissions = 0.0
        # Process emissions values and accumulate rounded values
        for emis_type, keys in emissions_keys.items():
            emis_value = next((data[k] for k in keys if data.get(k) is not None), None)
            if emis_value is not None:
                try:
                    val = round(float(emis_value), 2)
                    emissions_data[emis_type] = f"{val} mt"
                    total_emissions += val
                except (ValueError, TypeError):
                    emissions_data[emis_type] = f"{emis_value} mt"

        if total_emissions > 0:
            emissions_data["Total"] = f"{round(total_emissions, 2)} mt"

        self.full_width_table_section(
            pdf, "Emission", emissions_data,
            col1_ratio=50, col2_ratio=120
        )

        # Return the PDF as a file download.
        pdf_str = pdf.output(dest='S')
        pdf_buffer = BytesIO(pdf_str.encode('latin-1'))
        pdf_buffer.seek(0)

        headers = {
            'Content-Disposition': 'attachment; filename="mrv_report.pdf"'
        }

        return StreamingResponse(
            pdf_buffer,
            media_type='application/pdf',
            headers=headers
        )

    def merge_voyages(self, original, edited):
        original = self.exclude_non_mrv_voyages(original)
        merged = original.copy()
        for key, edited_entry in edited.items():
            if isinstance(edited_entry, dict) and edited_entry.get("is_deleted"):
                # For a deleted edited entry, remove all originals with the same voyage_id.
                voyage_id = edited_entry.get("voyage_id")
                keys_to_remove = []
                for orig_key in list(merged.keys()):
                    try:
                        if orig_key.split(":")[-1] == voyage_id:
                            keys_to_remove.append(orig_key)
                    except Exception:
                        continue
                for k in keys_to_remove:
                    del merged[k]
            else:
                # If the edited entry is not marked as deleted but contains a voyage_id,
                # remove any original entry that has the same voyage_id.
                if isinstance(edited_entry, dict) and "voyage_id" in edited_entry:
                    voyage_id = edited_entry["voyage_id"]
                    keys_to_remove = []
                    for orig_key in list(merged.keys()):
                        try:
                            if orig_key.split(":")[-1] == voyage_id:
                                keys_to_remove.append(orig_key)
                        except Exception:
                            continue
                    for k in keys_to_remove:
                        del merged[k]
                # Then, update/add the voyage from the edited data.
                merged[key] = edited_entry
        return original, merged

    def generate_report(self, vessel_imo, vessel_name, mrv_data, total_mrv_count, filtered_mrv_count):
        """
        Generate a PDF summary report for all voyages (totals) using the same
        section names and value labels as in the single-voyage report,
        with Cargo, Transport Work and Voyage Types included in the Voyage Information.
        """

        # Helper: nicely format large numbers
        def format_number(n):
            try:
                num = float(n)
                if abs(num) >= 1000:
                    return f"{num:,.2f}"
                else:
                    return f"{int(num)}" if num.is_integer() else f"{num:.2f}"
            except Exception:
                return str(n)

        # Turn the copy of voyages into a list
        voyages = list(mrv_data["mrv_data_copy"].values())

        # --- Aggregate Voyage Information ---
        # times
        starts = [v["start_time"] for v in voyages if v.get("start_time")]
        ends = [v["end_time"] for v in voyages if v.get("end_time")]
        departure_time = min(starts) if starts else "N/A"
        arrival_time = max(ends) if ends else "N/A"

        # total time at sea
        total_secs = 0
        for v in voyages:
            try:
                d = int(v.get("time_sailed_days", 0))
                h = int(v.get("time_sailed_hours", 0))
                m = int(v.get("time_sailed_minutes", 0))
                total_secs += d * 86400 + h * 3600 + m * 60
            except Exception:
                pass
        days = total_secs // 86400
        rem = total_secs % 86400
        hours = rem // 3600
        rem %= 3600
        minutes = rem // 60

        # total distance
        total_distance = 0.0
        for v in voyages:
            try:
                total_distance += float(v.get("distance", 0))
            except Exception:
                pass

        # Build the Voyage Information dict
        voyage_info = {
            "Departure": departure_time.replace("T", " ").split('.')[0]
            if ("T" in departure_time or "." in departure_time)
            else departure_time,
            "Arrival": arrival_time.replace("T", " ").split('.')[0]
            if ("T" in arrival_time or "." in arrival_time)
            else arrival_time,
            "Time at Sea": f"{days}d {hours}h {minutes}m",
            "Distance": f"{format_number(total_distance)} nm",
            "Cargo": f"{format_number(sum(float(v.get('cargo', 0)) for v in voyages))} mt",
            "Transport Work": f"{format_number(sum(float(v.get('transport_work', 0)) for v in voyages))} mt/nm",
            "Voyage Types": ", ".join(sorted({v.get("voyage_type", "") for v in voyages if v.get("voyage_type")})),
        }

        # --- Aggregate Fuel Consumption ---
        hf, md, lf = 0.0, 0.0, 0.0
        for v in voyages:
            try:
                hf += float(v.get("fuel_consumption_hfo", v.get("fuel_consumption_HFO", 0)))
            except:
                pass
            try:
                md += float(v.get("fuel_consumption_mdo", v.get("fuel_consumption_MDO", 0)))
            except:
                pass
            try:
                lf += float(v.get("fuel_consumption_lfo", v.get("fuel_consumption_LFO", 0)))
            except:
                pass

        hf, md, lf = round(hf, 2), round(md, 2), round(lf, 2)
        total_fuel = round(hf + md + lf, 2)
        fuel_info = {}
        if hf: fuel_info["HFO"] = f"{format_number(hf)} mt"
        if md: fuel_info["MDO"] = f"{format_number(md)} mt"
        if lf: fuel_info["LFO"] = f"{format_number(lf)} mt"
        if total_fuel > 0: fuel_info["Total"] = f"{format_number(total_fuel)} mt"

        # --- Aggregate Emissions ---
        eh, em, el = 0.0, 0.0, 0.0
        for v in voyages:
            try:
                eh += float(v.get("emissions_hfo", v.get("emissions_HFO", 0)))
            except:
                pass
            try:
                em += float(v.get("emissions_mdo", v.get("emissions_MDO", 0)))
            except:
                pass
            try:
                el += float(v.get("emissions_lfo", v.get("emissions_LFO", 0)))
            except:
                pass

        eh, em, el = round(eh, 2), round(em, 2), round(el, 2)
        total_em = round(eh + em + el, 2)
        emissions_info = {}
        if eh: emissions_info["HFO"] = f"{format_number(eh)} mt"
        if em: emissions_info["MDO"] = f"{format_number(em)} mt"
        if el: emissions_info["LFO"] = f"{format_number(el)} mt"
        if total_em > 0: emissions_info["Total"] = f"{format_number(total_em)} mt"

        # --- Vessel Information (High-Level) ---
        vessel_info = {
            "Name": vessel_name,
            "IMO": vessel_imo
        }

        # --- Build & Send PDF ---
        pdf = FPDF(orientation='P', unit='mm', format='A4')
        pdf.set_margins(15, 10, 15)
        pdf.set_auto_page_break(auto=True, margin=15)
        pdf.add_page()

        # Title
        pdf.set_font("Arial", "B", 16)
        pdf.cell(0, 10, "MRV Annual Report", ln=True, align="C")
        pdf.ln(2)

        # Completed‐voyages line
        pdf.set_font("Arial", "", 10)
        txt = f"{filtered_mrv_count} out of {total_mrv_count} voyages approved"
        pdf.cell(0, 4, txt, ln=True, align="C")
        pdf.ln(5)

        # Sections
        self.full_width_table_section(pdf, "Vessel Information", vessel_info, col1_ratio=50, col2_ratio=120)
        self.full_width_table_section(pdf, "Voyage Information", voyage_info, col1_ratio=50, col2_ratio=120)
        self.full_width_table_section(pdf, "Fuel Consumption", fuel_info, col1_ratio=50, col2_ratio=120)
        self.full_width_table_section(pdf, "Emission", emissions_info, col1_ratio=50, col2_ratio=120)

        # Render and return
        pdf_bytes = pdf.output(dest='S').encode('latin-1')
        buf = BytesIO(pdf_bytes)
        buf.seek(0)
        return StreamingResponse(
            buf,
            media_type="application/pdf",
            headers={
                'Content-Disposition': 'attachment; filename="mrv_summary_report.pdf"'
            }
        )

    def flag_voyages(self, mrv_data):
        # Mapping for required fields.
        required_field_mappings = {
            "departure": "start_time",
            "arrival": "end_time",
            "departurePort": "departure_port",
            "arrivalPort": "destination_port",
            "timeSailedDays": "time_sailed_days",
            "timeSailedHours": "time_sailed_hours",
            "timeSailedMinutes": "time_sailed_minutes",
            "distance": "distance",
            # Group 3: Approval
            "approvedMRVVoyage": "approved_mrv_voyage",
        }

        # Base keys for fuel consumption (we'll try lowercase then suffix‑uppercase)
        fuel_keys = [
            "fuel_consumption_mdo",
            "fuel_consumption_lfo",
            "fuel_consumption_hfo",
        ]

        def is_nonempty(val):
            return val is not None and str(val).strip() != ""

        def valid_fuel(val):
            """
            Reject None, empty, "0"/"0.0", or any float <= 0.
            """
            if val is None:
                return False
            s = str(val).strip()
            if s in ("", "0", "0.0"):
                return False
            try:
                return float(s) > 0
            except (ValueError, TypeError):
                return False

        def has_valid_fuel(voyage):
            """
            Try each fuel key; if invalid, try suffix‑uppercase fallback.
            """
            for key in fuel_keys:
                # 1) try lowercase key
                if valid_fuel(voyage.get(key)):
                    return True

                # 2) build fallback: upper‑case only the part after the last '_'
                if "_" in key:
                    prefix, suffix = key.rsplit("_", 1)
                    fallback_key = f"{prefix}_{suffix.upper()}"
                else:
                    fallback_key = key.upper()

                if valid_fuel(voyage.get(fallback_key)):
                    return True

            return False

        # Iterate and flag each voyage
        for vid, voyage in mrv_data.items():
            if not isinstance(voyage, dict):
                continue

            # a) all required fields nonempty
            required_complete = all(
                is_nonempty(voyage.get(field))
                for field in required_field_mappings.values()
            )
            # b) at least one valid fuel under lower‑ or suffix‑uppercase key
            fuel_data_complete = has_valid_fuel(voyage)
            # c) MRV Voyage is Approved.
            mrv_voyage_approved = voyage.get("approved_mrv_voyage")
            complete = required_complete and fuel_data_complete and mrv_voyage_approved
            # Flag the voyage based only on the computed completeness.
            voyage["status"] = "complete" if complete else "incomplete"

        return mrv_data

    def filter_complete(self, flagged_updated):
        return {
            key: voyage for key, voyage in flagged_updated.items()
            if isinstance(voyage, dict) and voyage.get("status") in "complete"
        }

    def get_final_object(self, json_data_original, json_data_edited, year):
        # Build your two dicts, keyed by voyage_id
        mrv_data = {r['voyage_id']: r for r in json_data_original}
        mrv_data_copy = {r['voyage_id']: r for r in json_data_edited}

        # Return a single-year (int) instead of a list
        return {
            'year': year,
            'mrv_data': mrv_data,
            'mrv_data_copy': mrv_data_copy
        }

    def convert_to_json(self, data):
        data = orjson.dumps(data)
        data = orjson.loads(data)
        return data

    def transform_data(self, data):
        def get_val(voyage, key):
            return voyage.get(key)

        def combine_time(voyage, prefix):
            days = get_val(voyage, f"{prefix}_days")
            hours = get_val(voyage, f"{prefix}_hours")
            minutes = get_val(voyage, f"{prefix}_minutes")
            if days is not None and hours is not None and minutes is not None:
                return f"{days}d {hours}h {minutes}m 0s"
            return None

        transformed_voyages = []
        for key, voyage in data.items():
            if "time_sailed" in voyage:
                voyage["time_sailed"] = combine_time(voyage, "time_sailed")
            if "time_at_sea" in voyage:
                voyage["time_at_sea"] = combine_time(voyage, "time_at_sea")
            if "time_in_port" in voyage:
                voyage["time_in_port"] = combine_time(voyage, "time_in_port")

            if "navigation_statuses" in voyage:
                voyage["navigation_statuses"] = (
                    str(voyage["navigation_statuses"])
                    if voyage["navigation_statuses"] is not None
                    else None
                )
            transformed_voyages.append(voyage)

        return transformed_voyages

    def get_converted_data(self, data):
        transform_data = self.transform_data(data)
        # relevant_data = pd.DataFrame(transform_data)
        # relevant_data = relevant_data.to_dict(orient='records')
        for row in transform_data:
            for key, value in row.items():
                if type(value) == float or type(value) == np.float64:
                    row[key] = float(round(value, 2))
            value = row.get('time_sailed')

            if isinstance(value, str) and any(x in value for x in ['d', 'h', 'm', 's']):
                parts = value.replace('s', '').split()
                days = next((p.replace('d', '') for p in parts if 'd' in p), '0')
                hours = next((p.replace('h', '') for p in parts if 'h' in p), '0')
                minutes = next((p.replace('m', '') for p in parts if 'm' in p), '0')
                row['time_sailed_days'] = int(days)
                row['time_sailed_hours'] = int(hours)
                row['time_sailed_minutes'] = int(minutes)
            row['fuel_types'] = self.fuel_types
            keys_to_round = [
                "fuel_consumption_hfo", "fuel_consumption_lfo", "fuel_consumption_mdo",
                "fuel_consumption_HFO", "fuel_consumption_LFO", "fuel_consumption_MDO",
            ]
            for k in keys_to_round:
                val = row.get(k)
                if val is not None and isinstance(val, (float, np.floating)):
                    row[k] = float(round(val, 2))
            row.pop('time_sailed', None)
            row.pop('time_at_sea', None)
        return transform_data
