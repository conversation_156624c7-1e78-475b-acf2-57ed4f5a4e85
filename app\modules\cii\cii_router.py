# CII Router - API endpoints for Carbon Intensity Indicator operations

from typing import Optional

from fastapi import APIRouter, Depends
from auth import owner_vat_check

from app.modules.cii.logic_helpers.cii import Cii
from app.modules.cii.cii_logic import CiiLogic
from app.modules.cii.cii_service import CiiService
from app.context.context import get_context
from app.context.utils import route_input

# Create router with CII tag
router = APIRouter(tags=["CII"])

# Set up dependency injection for logic, service, and helper layers
get_cii_params, get_cii_logic = get_context(
    CiiLogic,
    CiiService,
    Cii,
)


@router.get("/cii", dependencies=[Depends(owner_vat_check)])
async def cii_read(
        params: dict = Depends(get_cii_params),
        logic: CiiLogic = Depends(get_cii_logic),
):
    # GET endpoint to read CII data for vessel and year
    return await logic.cii_read(**params)


@router.get("/cii-years", dependencies=[Depends(owner_vat_check)])
async def cii_years(
        params: dict = Depends(get_cii_params),
        logic: CiiLogic = Depends(get_cii_logic),
):
    # GET endpoint to get available CII years for a vessel
    return await logic.cii_years(**params)


@router.get("/cii/reset", dependencies=[Depends(owner_vat_check)])
@router.get("/cii/reset/{all}", dependencies=[Depends(owner_vat_check)])
async def cii_reset(
        all: Optional[str] = None,
        params: dict = Depends(get_cii_params),
        logic: CiiLogic = Depends(get_cii_logic),
):
    # GET endpoint to reset CII data - single week or entire year
    return await logic.cii_reset(all=all, **params)


@router.put("/cii")
@route_input(include_body=True)
async def cii_update(
        params: dict = Depends(get_cii_params),
        logic: CiiLogic = Depends(get_cii_logic),
):
    # PUT endpoint to update CII data with new values
    return await logic.cii_update(**params)
