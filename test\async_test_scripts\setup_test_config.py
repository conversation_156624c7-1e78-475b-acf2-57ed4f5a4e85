#!/usr/bin/env python3
"""
Interactive setup script for async test configuration.
This script helps you configure the test environment for real endpoint testing.
"""

import os
import json
from pathlib import Path


def get_user_input(prompt, default=None, required=True):
    """Get user input with optional default value."""
    if default:
        full_prompt = f"{prompt} [{default}]: "
    else:
        full_prompt = f"{prompt}: "
    
    while True:
        value = input(full_prompt).strip()
        
        if value:
            return value
        elif default:
            return default
        elif not required:
            return None
        else:
            print("This field is required. Please enter a value.")


def test_jwt_secret(jwt_secret):
    """Test if JWT secret works by trying to create a token."""
    try:
        import jwt
        from datetime import datetime, timedelta
        
        payload = {
            "exp": datetime.utcnow() + timedelta(hours=1),
            "test": True
        }
        token = jwt.encode(payload, jwt_secret, algorithm="HS256")
        decoded = jwt.decode(token, jwt_secret, algorithms=["HS256"])
        return True
    except Exception as e:
        print(f"JWT test failed: {e}")
        return False


def main():
    print("🔧 FastAPI Async Test Configuration Setup")
    print("=" * 50)
    print("This script will help you configure the test environment.")
    print("You'll need access to your FastAPI server and test data.")
    print()
    
    # Get server configuration
    print("📡 Server Configuration")
    base_url = get_user_input("FastAPI server URL", "http://localhost:8769")
    
    # Get JWT secret
    print("\n🔐 Authentication")
    jwt_secret = os.getenv("JWT_SECRET")
    if jwt_secret:
        print(f"Found JWT_SECRET in environment: {jwt_secret[:10]}...")
        use_env_jwt = get_user_input("Use this JWT secret? (y/n)", "y").lower() == 'y'
        if not use_env_jwt:
            jwt_secret = None
    
    if not jwt_secret:
        jwt_secret = get_user_input("JWT Secret (from your .env file)")
    
    # Test JWT secret
    if not test_jwt_secret(jwt_secret):
        print("⚠️  JWT secret test failed. Please verify the secret is correct.")
        return
    
    print("✅ JWT secret is valid")
    
    # Get test data
    print("\n📊 Test Data Configuration")
    print("You'll need valid test data from your system:")
    
    owner_vat = get_user_input("Owner VAT number")
    session_owner_vat = get_user_input("Session Owner VAT", owner_vat)
    vessel_imo = get_user_input("Vessel IMO number")
    vessel_id = get_user_input("Vessel ID (numeric)", "1")
    vessel_name = get_user_input("Vessel Name", "Test Vessel")
    
    # Get date ranges
    print("\n📅 Date Ranges")
    print("Use dates where you have actual data in your system:")
    from_date = get_user_input("From Date (YYYY-MM-DD HH:MM:SS)", "2024-01-01 00:00:00")
    to_date = get_user_input("To Date (YYYY-MM-DD HH:MM:SS)", "2024-01-07 23:59:59")
    start_date = get_user_input("Start Date (YYYY-MM-DD)", "2024-01-01")
    end_date = get_user_input("End Date (YYYY-MM-DD)", "2024-01-07")
    
    # Create configuration
    config = {
        "base_url": base_url,
        "jwt_secret": jwt_secret,
        "owner_vat": owner_vat,
        "session_owner_vat": session_owner_vat,
        "vessel_imo": vessel_imo,
        "vessel_id": int(vessel_id),
        "vessel_name": vessel_name,
        "from_date": from_date,
        "to_date": to_date,
        "start_date": start_date,
        "end_date": end_date
    }
    
    # Generate test_config.py content
    config_content = f'''"""
Configuration file for async testing with real endpoints.
Generated by setup_test_config.py
"""

import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Test Configuration
TEST_CONFIG = {{
    # Server Configuration
    "base_url": "{config['base_url']}",
    "jwt_secret": os.getenv("JWT_SECRET", "{config['jwt_secret']}"),
    
    # Authentication & Authorization
    "owner_vat": "{config['owner_vat']}",
    "session_owner_vat": "{config['session_owner_vat']}",
    
    # Test Vessel Data
    "vessel_imo": "{config['vessel_imo']}",
    "vessel_id": {config['vessel_id']},
    "vessel_name": "{config['vessel_name']}",
    
    # Date Ranges for Testing
    "from_date": "{config['from_date']}",
    "to_date": "{config['to_date']}",
    "start_date": "{config['start_date']}",
    "end_date": "{config['end_date']}"
}}

# Endpoint Categories for Testing
ENDPOINTS = {{
    "quick": [
        "/owners",
        "/vessels",
    ],
    "medium": [
        "/hull-performance-ai",
        "/hull-performance-raw",
        "/anomaly-detection",
    ],
    "heavy": [
        "/data-analytics",
        "/export/json",
    ]
}}

def get_test_config():
    """Get the test configuration."""
    return TEST_CONFIG.copy()

def get_endpoints():
    """Get the endpoint categories."""
    return ENDPOINTS.copy()

def validate_config():
    """Validate that the configuration is properly set up."""
    return []  # Configuration was set up by this script

def print_config_help():
    """Print help for configuring the test environment."""
    print("Configuration was set up by setup_test_config.py")
    print("Edit test_config.py manually if you need to make changes.")
'''
    
    # Write configuration file
    config_path = Path(__file__).parent / "test_config.py"
    with open(config_path, 'w') as f:
        f.write(config_content)
    
    print("\n✅ Configuration Complete!")
    print(f"Configuration saved to: {config_path}")
    print()
    print("🚀 You can now run the async tests:")
    print("   python test_concurrent_requests.py")
    print("   python manual_test_commands.py")
    print("   python ddos_load_test.py")
    print()
    print("📝 Configuration Summary:")
    print(f"   Server: {config['base_url']}")
    print(f"   Owner VAT: {config['owner_vat']}")
    print(f"   Vessel IMO: {config['vessel_imo']}")
    print(f"   Date Range: {config['from_date']} to {config['to_date']}")


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n❌ Setup cancelled by user.")
    except Exception as e:
        print(f"\n❌ Setup failed: {e}")
        print("Please check your inputs and try again.")
