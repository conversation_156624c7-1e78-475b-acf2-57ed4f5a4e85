# MRV Service - handles data access for MRV (Monitoring, Reporting, Verification) operations

from app.services.redis_api.redis_api_client import RedisApi


class MrvService:
    # Service layer for MRV data operations

    def __init__(self, redis_client: RedisApi = None):
        # Initialize with Redis client (create new one if not provided)
        self.redis = redis_client or RedisApi()

    async def get_data(self, **params):
        # Get MRV data from Redis API
        return await self.redis.call(
            path="get_mrv",
            key="mrv",
            **params
        )

    # Commented out - not currently used
    # async def get_report(self, **params):
    #     return await self.redis_api.call(
    #         path="get_mrv_report",
    #         key="report",
    #         **params
    #     )

    async def get_voyage_data(self, **params):
        # Get specific voyage data from Redis API
        return await self.redis.call(
            path="get_mrv_voyage_data",
            key="mrv_voyage",
            **params
        )

    async def create_voyage(self, payload: dict):
        # Create new voyage record via Redis API
        return await self.redis.call(
            path="create_mrv_voyage",
            method="POST",
            params=None,
            json=payload,
            expected_status=201,
        )

    async def update_voyage(self, payload: dict):
        # Update existing voyage record via Redis API
        return await self.redis.call(
            path="update_mrv_voyage",
            method="PUT",
            params=None,
            json=payload,
        )

    async def delete_voyage(self, **params):
        # Delete voyage record via Redis API
        return await self.redis.call(
            path="delete_mrv_voyage",
            method="DELETE",
            **params
        )
