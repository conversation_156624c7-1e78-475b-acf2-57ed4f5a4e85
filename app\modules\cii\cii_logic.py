# CII Logic - handles business logic for Carbon Intensity Indicator operations

from typing import Optional

from fastapi import Body

from app.modules.cii.logic_helpers.cii import Cii
from .cii_service import CiiService
from app.context.utils import rename_vat


class CiiLogic:
    # Logic layer for CII (Carbon Intensity Indicator) operations
    def __init__(self, cii_service: CiiService, cii: Cii):
        self.cii_service = cii_service
        self.cii = cii

    async def cii_read(self, vessel_imo: int, owner_vat: str, year: int):
        # Get CII data for specific vessel and year
        return await self.cii_service.get_cii_data(rename_vat(**locals()))

    async def cii_years(self, vessel_imo: int, owner_vat: str):
        # Get available years for CII data for a vessel
        return await self.cii_service.get_cii_years(rename_vat(**locals()))

    async def cii_reset(
            self,
            vessel_imo: str,
            owner_vat: str,
            year: str,
            week_numb: Optional[str] = None,
            all: Optional[str] = None,
    ):
        # Reset CII data - either single week or entire year
        # Mirrors Flask behavior: /cii/reset?week_numb=N or /cii/reset/all
        payload = {
            "vessel_imo": vessel_imo,
            "owner_vat": owner_vat,
            "year": year,
        }

        if all is None:
            # Single week reset - week_numb is required
            if week_numb is None:
                raise ValueError(
                    "Missing required query parameter 'week_numb' for single‑week reset"
                )
            payload["week_numb"] = week_numb

        return await self.cii.cii_reset(**payload)

    async def cii_update(self, updated_values: dict = Body(...)):
        # Update CII data with new values
        return await self.cii.cii_update(updated_values)
