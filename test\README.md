# Testing Documentation

This document provides comprehensive information about the testing suite for the FastAPI backend application.

## Overview

The testing suite is organized into three main categories:
- **Unit Tests**: Test individual components in isolation
- **Integration Tests**: Test component interactions and API endpoints
- **System Tests**: Test complete workflows and system behavior

## Test Structure

```
test/
├── conftest.py                 # Global test fixtures and configuration
├── requirements.txt            # Test dependencies
├── utils/
│   └── test_helpers.py        # Test utility classes and helpers
├── unit/                      # Unit tests
│   ├── test_auth.py          # Authentication module tests
│   ├── test_settings.py      # Settings configuration tests
│   ├── test_redis_client.py  # Redis client tests
│   ├── test_context.py       # Context system tests
│   ├── test_home_logic.py    # Home logic tests
│   ├── test_home_service.py  # Home service tests
│   └── test_context_utils.py # Context utility tests
├── integration/               # Integration tests
│   ├── test_api_endpoints.py # API endpoint integration tests
│   ├── test_redis_integration.py # Redis integration tests
│   └── test_module_interactions.py # Module interaction tests
└── system/                    # System tests
    ├── test_end_to_end.py    # End-to-end workflow tests
    └── test_performance_load.py # Performance and load tests
```

## Prerequisites

### Install Test Dependencies

```bash
pip install -r test/requirements.txt
```

### Environment Setup

Ensure you have the following environment variables set for testing:
- `REDIS_URL`: Redis connection URL (can be mocked for tests)
- `JWT_SECRET_KEY`: JWT secret for authentication testing
- `API_BASE_URL`: Base URL for external API calls

## Running Tests

### Run All Tests

```bash
# Run all tests
pytest

# Run with coverage
pytest --cov=app --cov-report=html --cov-report=term
```

### Run by Category

```bash
# Unit tests only
pytest test/unit/

# Integration tests only
pytest test/integration/

# System tests only
pytest test/system/
```

### Run by Markers

```bash
# Run only unit tests
pytest -m unit

# Run only integration tests
pytest -m integration

# Run only system tests
pytest -m system

# Run performance tests
pytest -m performance

# Run load tests
pytest -m load

# Run stress tests
pytest -m stress
```

### Run Specific Test Files

```bash
# Run authentication tests
pytest test/unit/test_auth.py

# Run API endpoint tests
pytest test/integration/test_api_endpoints.py

# Run end-to-end tests
pytest test/system/test_end_to_end.py
```

### Run with Specific Options

```bash
# Run with verbose output
pytest -v

# Run with detailed output
pytest -vv

# Run and stop on first failure
pytest -x

# Run failed tests from last run
pytest --lf

# Run tests in parallel (requires pytest-xdist)
pytest -n auto

# Run with specific log level
pytest --log-level=DEBUG
```

## Test Configuration

### Pytest Configuration (pytest.ini)

The test suite is configured with:
- Async support enabled
- Coverage reporting
- Test markers for categorization
- Custom test discovery patterns

### Test Markers

- `@pytest.mark.unit`: Unit tests
- `@pytest.mark.integration`: Integration tests
- `@pytest.mark.system`: System tests
- `@pytest.mark.performance`: Performance tests
- `@pytest.mark.load`: Load tests
- `@pytest.mark.stress`: Stress tests
- `@pytest.mark.asyncio`: Async tests

## Test Fixtures

### Global Fixtures (conftest.py)

- `mock_redis_client`: Mocked Redis client for testing
- `auth_headers`: Authentication headers for API tests
- `test_app`: FastAPI test application instance
- `async_client`: Async HTTP client for API testing
- `mock_auth_dependencies`: Mocked authentication dependencies
- `performance_test_helper`: Helper for performance testing
- `api_test_helper`: Helper for API testing

### Using Fixtures

```python
@pytest.mark.asyncio
async def test_example(async_client, auth_headers):
    response = await async_client.get("/endpoint", headers=auth_headers)
    assert response.status_code == 200
```

## Writing Tests

### Unit Test Example

```python
import pytest
from unittest.mock import AsyncMock
from app.modules.home.home_logic import HomeLogic

class TestHomeLogic:
    @pytest.fixture
    def mock_dependencies(self):
        return {
            "home_service": AsyncMock(),
            "data_controller": AsyncMock(),
            "measurements": AsyncMock()
        }
    
    @pytest.mark.asyncio
    async def test_home_method(self, mock_dependencies):
        logic = HomeLogic(**mock_dependencies)
        result = await logic.home(owner_vat="NO950700939")
        assert result is not None
```

### Integration Test Example

```python
@pytest.mark.integration
@pytest.mark.asyncio
async def test_api_endpoint(async_client, auth_headers):
    response = await async_client.get(
        "/users?owner_vat=NO950700939",
        headers=auth_headers
    )
    assert response.status_code == 200
    assert "users" in response.json()
```

### System Test Example

```python
@pytest.mark.system
@pytest.mark.asyncio
async def test_complete_workflow(async_client, auth_headers):
    # Step 1: Get home data
    home_response = await async_client.get("/home", headers=auth_headers)
    assert home_response.status_code == 200
    
    # Step 2: Use home data for analytics
    analytics_response = await async_client.post(
        "/data-analytics",
        json={"data": "from_home"},
        headers=auth_headers
    )
    assert analytics_response.status_code == 200
```

## Mocking Guidelines

### Redis Mocking

```python
with patch('app.services.redis_api.redis_api_client.aioredis') as mock_aioredis:
    mock_connection = AsyncMock()
    mock_aioredis.from_url.return_value = mock_connection
    mock_connection.get.return_value = '{"test": "data"}'
    
    # Your test code here
```

### Authentication Mocking

```python
with patch('auth.validate_owner_access') as mock_validate:
    mock_validate.return_value = True
    # Your test code here
```

### HTTP Client Mocking

```python
with patch('httpx.AsyncClient.request') as mock_request:
    mock_response = AsyncMock()
    mock_response.json.return_value = {"result": "success"}
    mock_response.status_code = 200
    mock_request.return_value = mock_response
    
    # Your test code here
```

## Performance Testing

### Performance Test Guidelines

1. **Response Time**: Measure and assert on response times
2. **Throughput**: Test requests per second capacity
3. **Concurrency**: Test concurrent request handling
4. **Memory Usage**: Monitor memory consumption
5. **Load Testing**: Test under sustained load

### Performance Test Example

```python
@pytest.mark.performance
@pytest.mark.asyncio
async def test_endpoint_performance(async_client, performance_test_helper):
    results = await performance_test_helper.measure_concurrent_requests(
        async_client,
        "/endpoint",
        num_requests=50
    )
    
    assert results["success_rate"] >= 0.95
    assert results["avg_response_time"] < 1.0
```

## Continuous Integration

### GitHub Actions Workflow

The test suite is configured to run automatically on:
- Pull requests
- Pushes to main branch
- Scheduled runs (daily)

### Test Stages

1. **Unit Tests**: Fast, isolated tests
2. **Integration Tests**: Component interaction tests
3. **System Tests**: End-to-end workflow tests
4. **Performance Tests**: Load and performance validation

## Troubleshooting

### Common Issues

1. **Redis Connection Errors**: Ensure Redis is running or properly mocked
2. **Authentication Failures**: Check JWT configuration and mocking
3. **Async Test Issues**: Ensure `@pytest.mark.asyncio` is used
4. **Import Errors**: Check Python path and dependencies

### Debug Mode

```bash
# Run tests with debug output
pytest --log-level=DEBUG -s

# Run specific test with debugging
pytest test/unit/test_auth.py::TestAuthHandler::test_method -vv -s
```

### Coverage Reports

```bash
# Generate HTML coverage report
pytest --cov=app --cov-report=html

# View coverage report
open htmlcov/index.html
```

## Best Practices

1. **Test Isolation**: Each test should be independent
2. **Descriptive Names**: Use clear, descriptive test names
3. **Arrange-Act-Assert**: Structure tests clearly
4. **Mock External Dependencies**: Mock Redis, HTTP clients, etc.
5. **Test Edge Cases**: Include error conditions and edge cases
6. **Performance Awareness**: Include performance assertions
7. **Documentation**: Document complex test scenarios

## Contributing

When adding new tests:

1. Follow the existing test structure
2. Use appropriate test markers
3. Include both positive and negative test cases
4. Add performance tests for new endpoints
5. Update this documentation if needed

## Test Data

### Sample Test Data

```python
SAMPLE_OWNER_VAT = "NO950700939"
SAMPLE_VESSEL_IMO = "8918461"
SAMPLE_VESSEL_NAME = "Wilson Saga"
SAMPLE_DATE_RANGE = {
    "from_date": "2024-01-01 00:00:00",
    "to_date": "2024-01-07 23:59:59"
}
```

### Test Factories

Use the MockFactory class for generating test data:

```python
from test.utils.test_helpers import MockFactory

factory = MockFactory()
vessel_data = factory.create_vessel_data()
user_data = factory.create_user_data()
```
