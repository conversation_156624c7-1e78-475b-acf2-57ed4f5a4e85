"""
Configuration file for async testing with real endpoints.
Update these values to match your test environment.
"""

import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Test Configuration
TEST_CONFIG = {
    # Server Configuration
    "base_url": "http://localhost:8769",
    "jwt_secret": os.getenv("JWT_SECRET", "your-jwt-secret-key"),
    
    # Authentication & Authorization
    "owner_vat": "NO950700939",  # Replace with actual test owner VAT
    "session_owner_vat": "NO950700939",  # Replace with actual session owner VAT
    
    # Test Vessel Data
    "vessel_imo": "8918461",  # Replace with actual test vessel IMO
    "vessel_id": 32,  # Replace with actual test vessel ID
    "vessel_name": "Wilson Saga",  # Replace with actual vessel name
    
    # Date Ranges for Testing
    "from_date": "2024-01-01 00:00:00",
    "to_date": "2024-01-07 23:59:59",
    "start_date": "2024-01-01",
    "end_date": "2024-01-07"
}

# Endpoint Categories for Testing
ENDPOINTS = {
    "quick": [
        "/owners",
        "/vessels",
    ],
    "medium": [
        "/hull-performance-ai",
        "/hull-performance-raw",
        "/anomaly-detection",
    ],
    "heavy": [
        "/data-analytics",
        "/export/json",
    ],
    "post_endpoints": [
        "/efficiency",  # POST endpoint
    ]
}

# Test Scenarios
TEST_SCENARIOS = {
    "basic_async": {
        "description": "Test that quick endpoints don't block when heavy endpoints are running",
        "heavy_endpoint": "/data-analytics",
        "quick_endpoints": ["/owners", "/vessels"],
        "expected_quick_response_time": 2.0  # seconds
    },
    "multiple_heavy": {
        "description": "Test multiple heavy endpoints running concurrently",
        "endpoints": ["/data-analytics", "/hull-performance-ai", "/hull-performance-raw"],
        "expected_concurrent_execution": True
    },
    "load_test": {
        "description": "Test server performance under load",
        "endpoints": ["/owners", "/vessels", "/data-analytics", "/hull-performance-ai"],
        "num_requests": 20,
        "success_rate_threshold": 0.90  # 90% success rate
    }
}

def get_test_config():
    """Get the test configuration."""
    return TEST_CONFIG.copy()

def get_endpoints():
    """Get the endpoint categories."""
    return ENDPOINTS.copy()

def get_test_scenarios():
    """Get the test scenarios."""
    return TEST_SCENARIOS.copy()

def validate_config():
    """Validate that the configuration is properly set up."""
    issues = []
    
    if TEST_CONFIG["jwt_secret"] == "your-jwt-secret-key":
        issues.append("JWT_SECRET not configured - update test_config.py or set JWT_SECRET environment variable")
    
    if TEST_CONFIG["owner_vat"] == "123456789":
        issues.append("owner_vat not configured - update with actual test owner VAT")
    
    if TEST_CONFIG["vessel_imo"] == "123456789":
        issues.append("vessel_imo not configured - update with actual test vessel IMO")
    
    return issues

def print_config_help():
    """Print help for configuring the test environment."""
    print("🔧 Test Configuration Help")
    print("=" * 50)
    print("To run tests with real endpoints, you need to configure:")
    print()
    print("1. JWT_SECRET:")
    print("   - Set JWT_SECRET environment variable, or")
    print("   - Update jwt_secret in test_config.py")
    print()
    print("2. Test Data:")
    print("   - owner_vat: Valid owner VAT number for your test environment")
    print("   - session_owner_vat: Session owner VAT (usually same as owner_vat)")
    print("   - vessel_imo: Valid vessel IMO number")
    print("   - vessel_id: Valid vessel ID")
    print()
    print("3. Date Ranges:")
    print("   - from_date/to_date: Date range with available data")
    print("   - start_date/end_date: Date range for hull performance queries")
    print()
    print("4. Server URL:")
    print("   - base_url: Your FastAPI server URL (default: http://localhost:8769)")
    print()
    print("Edit test_config.py to update these values.")
    print("=" * 50)

if __name__ == "__main__":
    print_config_help()
    print()
    
    issues = validate_config()
    if issues:
        print("⚠️  Configuration Issues:")
        for issue in issues:
            print(f"   - {issue}")
    else:
        print("✅ Configuration looks good!")
