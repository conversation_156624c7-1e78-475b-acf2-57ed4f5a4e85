from typing import Optional
from fastapi import HTTPException
from fastapi import status as http_status


class RedisApiError(HTTPException):
    """
    Custom exception class for Redis API errors.
    Extends FastAPI's HTTPException to provide specialized error handling for Redis API operations.
    
    Key features:
    - Inherits from HTTPException for FastAPI integration
    - Provides detailed error messages
    - Uses appropriate HTTP status codes (defaults to 502 Bad Gateway)
    - Supports error cause chaining for better debugging
    """
    def __init__(
        self,
        detail: str,  # Human-readable error message
        status_code: int = http_status.HTTP_502_BAD_GATEWAY,  # Default to 502 Bad Gateway
        *,  # Force keyword-only arguments after this
        cause: Optional[Exception] = None  # Original exception that caused this error
    ):
        # Validate the HTTP status code is in the error range (400-599)
        # This ensures we don't accidentally use success codes for errors
        if not (400 <= status_code <= 599):
            raise ValueError(f"Invalid HTTP status code for error: {status_code}")

        # Initialize parent HTTPException with status code and error message
        # This ensures proper FastAPI error handling
        super().__init__(status_code=status_code, detail=detail)

        # Set up exception chaining if a cause was provided
        # This preserves the original error's traceback for debugging
        if cause is not None:
            self.__cause__ = cause

