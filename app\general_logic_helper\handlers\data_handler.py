from orjson import orjson
import logging
import traceback
from app.general_logic_helper.helpers.data.monthly_fuel_data import MonthlyFuelDataHelper
from app.general_logic_helper.helpers.charts.donut_chart_helper import DonutChartHelper
from app.general_logic_helper.helpers.charts.pie_chart_helper import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from app.general_logic_helper.helpers.data.summary_helper import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from app.general_logic_helper.helpers.data.data_analytics_helper import DataAnalyticsHelper


def calculate_fuel_consumption(data, vessel_imo, log_speed_reliable):
    data_list = []
    key = "me_1_total_6"
    frugal_status = None
    start_fc = None
    on_value = 0
    off_value = 0
    index = 0
    speed_type = "gspd" if log_speed_reliable == "gps_speed" else "lspd"
    speed_boundary = 4
    if vessel_imo in [9525572]:  # Key Stream
        speed_boundary = 8.5
    try:
        for element in data:
            # Calculate total fuel consumption the new way
            if isinstance(element["flwt"], str) and element.get("flwt") != "NULL":
                element["flwt"] = orjson.loads(element["flwt"])
            if element.get("sstat") is True and (element.get(speed_type) >= speed_boundary):
                if (
                    element["flwt"] is not None
                    and "me_1_total_6" in element["flwt"]
                    and element["flwt"][key] > 0.0
                    and vessel_imo != 9195391  # Ignore Vón Feeder
                    and vessel_imo != 9379478  # Ignore Ayse Ana
                ):
                    fc_total_element = element["flwt"][key]
                    # Set Frugal Status
                    if frugal_status is None and start_fc is None:
                        frugal_status = element["fstat"]
                        start_fc = fc_total_element

                    if element["fstat"] != frugal_status or index == len(data) - 1:
                        # If Frugal Status changed from previous element, calculate FC
                        end_fc = fc_total_element

                        if start_fc is not None and end_fc is not None:
                            net_flow = start_fc - end_fc
                            if frugal_status >= 1:
                                on_value += abs(net_flow)
                            else:
                                off_value += abs(net_flow)
                            # Start Over
                            start_fc = fc_total_element
                            frugal_status = element["fstat"]
                else:
                    # If switching from new to old method of collecting fuel consumption data
                    if start_fc is not None and key in data[index - 1]["flwt"]:
                        net_flow = start_fc - data[index - 1]["flwt"][key]
                        if frugal_status >= 1:
                            on_value += abs(net_flow)
                        else:
                            off_value += abs(net_flow)
                        # Reset
                        start_fc = None
                        frugal_status = None
                    # Calculate total fuel consumption the old way
                    if element["mefcm"] is not None and element["fstat"] is not None:
                        if element["fstat"] >= 1:
                            on_value += element["mefcm"] / 60 / 1000
                        else:
                            off_value += element["mefcm"] / 60 / 1000
            index += 1
        data_list.append(round(on_value, 2))
        data_list.append(round(off_value, 2))
    except Exception as e:
        logging.error(
            "Error while calculating total FC data for vessel underway %s in value_server: %s",
            vessel_imo,
            e,
        )
        print(traceback.format_exc())
    return data_list


class DataHandler:
    def generate_monthly_fuel(self, data, _vessel_id):
        MonthlyFuelDataHelper.generate_monthly_fuel_data(data, "Tons", _vessel_id)
        MonthlyFuelDataHelper.generate_months_to_use(data, _vessel_id)

    @staticmethod
    def init_donut_helper(data, _vessel_name, log_speed_reliable, vessel_imo):
        return DonutChartHelper.init_data(data, _vessel_name, log_speed_reliable, vessel_imo)

    @staticmethod
    def init_pie_helper(redis_data, vessel_imo, log_speed_reliable):
        fuel_underway = calculate_fuel_consumption(redis_data["measurements"], vessel_imo, log_speed_reliable)
        redis_data["reports"]["fpfc"] = PieChartHelper.init_data(
            redis_data["measurements"],
            fuel_underway=fuel_underway,
            log_speed_reliable=log_speed_reliable,
            vessel_imo=vessel_imo
        )

    def init_summary_helper(self, data):
        if data is not None and len(data) > 0:
            return SummaryHelper.init_data(data)

    @staticmethod
    def init_data_analytics_helper(
        redis_data, log_speed_reliable, ais_data_df, weather_data_df, vessel_id=None, vessel_imo=None
    ):
        from_date = redis_data["measurements"][0]["timestamp"]
        to_date = redis_data["measurements"][-1]["timestamp"]
        redis_data["data_analytics"] = DataAnalyticsHelper.init_data(
            redis_data,
            from_date,
            to_date,
            log_speed_reliable,
            ais_data_df,
            weather_data_df,
            vessel_id,
            vessel_imo
        )
