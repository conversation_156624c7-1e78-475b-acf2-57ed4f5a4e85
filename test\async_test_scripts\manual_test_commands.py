#!/usr/bin/env python3
"""
Manual test commands for async functionality using REAL endpoints.
This script provides simple commands to test async endpoints manually.
"""

import requests
import time
import threading
import jwt
from concurrent.futures import ThreadPoolExecutor, as_completed
from datetime import datetime, timedelta
from test_config import get_test_config, get_endpoints, validate_config, print_config_help


class ManualTester:
    def __init__(self):
        self.config = get_test_config()
        self.base_url = self.config["base_url"]
        self.session = requests.Session()
        self._setup_session()

    def _setup_session(self):
        """Set up the requests session with authentication headers."""
        token = self._generate_jwt_token()
        self.session.headers.update({
            "X-API-Key": token,
            "X-Tenant-Owner-Vat": self.config["session_owner_vat"],
            "Content-Type": "application/json"
        })

    def _generate_jwt_token(self):
        """Generate a JWT token for authentication."""
        payload = {
            "exp": datetime.utcnow() + timedelta(hours=1),
            "iat": datetime.utcnow(),
            "test": True
        }
        return jwt.encode(payload, self.config["jwt_secret"], algorithm="HS256")

    def make_request(self, endpoint, request_id=None, params=None):
        """Make a single request and print the result."""
        url = f"{self.base_url}{endpoint}"
        start_time = time.time()

        # Merge default params with provided params
        request_params = {**self.config}
        if params:
            request_params.update(params)

        try:
            response = self.session.get(url, params=request_params)
            end_time = time.time()
            duration = end_time - start_time

            print(f"Request {request_id or ''} to {endpoint}:")
            print(f"  Status: {response.status_code}")
            print(f"  Duration: {duration:.2f}s")

            if response.status_code == 200:
                try:
                    data = response.json()
                    print(f"  Response: Success (data length: {len(str(data))} chars)")
                except:
                    print(f"  Response: Success (non-JSON response)")
            else:
                print(f"  Response: Error - {response.text[:200]}...")
            print()

            return {
                "endpoint": endpoint,
                "status": response.status_code,
                "duration": duration,
                "success": response.status_code == 200
            }
        except Exception as e:
            end_time = time.time()
            duration = end_time - start_time
            print(f"Request {request_id or ''} to {endpoint} FAILED:")
            print(f"  Error: {e}")
            print(f"  Duration: {duration:.2f}s")
            print()
            return None


    def test_basic_async(self):
        """Test basic async functionality with real endpoints."""
        print("🧪 Testing Basic Async Functionality (Real Endpoints)")
        print("This will start data-analytics (heavy) and then call quick endpoints")
        print("The quick endpoints should respond immediately\n")

        # Use ThreadPoolExecutor to simulate concurrent requests
        with ThreadPoolExecutor(max_workers=5) as executor:
            # Submit all requests at roughly the same time
            futures = []

            # Heavy request
            futures.append(executor.submit(self.make_request, "/data-analytics", "HEAVY"))

            # Quick requests
            for i in range(3):
                endpoint = ["/owners", "/vessels"][i % 2]
                futures.append(executor.submit(self.make_request, endpoint, f"QUICK-{i+1}"))

            # Wait for all to complete
            for future in as_completed(futures):
                result = future.result()


    def test_multiple_heavy_endpoints(self):
        """Test multiple heavy endpoints."""
        print("🧪 Testing Multiple Heavy Endpoints")
        print("Starting data-analytics and hull-performance endpoints")
        print("Testing concurrent heavy processing\n")

        with ThreadPoolExecutor(max_workers=3) as executor:
            futures = []

            # Very heavy endpoint
            futures.append(executor.submit(self.make_request, "/data-analytics", "HEAVY"))

            # Medium heavy endpoints (start slightly after)
            time.sleep(1)
            futures.append(executor.submit(self.make_request, "/hull-performance-ai", "MEDIUM-1"))
            futures.append(executor.submit(self.make_request, "/hull-performance-raw", "MEDIUM-2"))

            # Wait for all to complete
            for future in as_completed(futures):
                result = future.result()


    def test_load(self, num_requests=10):
        """Test load with multiple concurrent requests to real endpoints."""
        print(f"🧪 Testing Load ({num_requests} concurrent requests)")
        print("Sending multiple requests to real endpoints\n")

        start_time = time.time()
        endpoints = ["/owners", "/vessels", "/data-analytics", "/hull-performance-ai"]

        with ThreadPoolExecutor(max_workers=num_requests) as executor:
            futures = []
            for i in range(num_requests):
                endpoint = endpoints[i % len(endpoints)]
                futures.append(executor.submit(self.make_request, endpoint, f"LOAD-{i+1}"))

            results = []
            for future in as_completed(futures):
                result = future.result()
                if result:
                    results.append(result)

        end_time = time.time()
        total_time = end_time - start_time

        print(f"Load test completed in {total_time:.2f}s")
        successful = [r for r in results if r.get("success", False)]
        print(f"Successful requests: {len(successful)}/{num_requests}")

        if successful:
            durations = [r["duration"] for r in successful]
            print(f"Average response time: {sum(durations)/len(durations):.2f}s")
            print(f"Max response time: {max(durations):.2f}s")
            print(f"Min response time: {min(durations):.2f}s")


    def test_health(self):
        """Test simple endpoint for health check."""
        print("🧪 Testing Health Check (Owners endpoint)")
        self.make_request("/owners", "HEALTH")

    def test_individual_endpoints(self):
        """Test individual endpoints one by one."""
        endpoints = get_endpoints()

        print("🧪 Testing Individual Endpoints")
        print("Testing each endpoint category:\n")

        for category, endpoint_list in endpoints.items():
            print(f"--- {category.upper()} ENDPOINTS ---")
            for endpoint in endpoint_list:
                self.make_request(endpoint, f"{category.upper()}")
                time.sleep(1)  # Small delay between requests
            print()


def main():
    """Main menu for manual testing."""
    print("🚀 FastAPI Async Manual Testing (Real Endpoints)")

    # Validate configuration
    config_issues = validate_config()
    if config_issues:
        print("❌ Configuration issues found:")
        for issue in config_issues:
            print(f"   - {issue}")
        print()
        print_config_help()
        return

    config = get_test_config()
    print(f"Server: {config['base_url']}")
    print(f"Owner VAT: {config['owner_vat']}")
    print(f"Vessel IMO: {config['vessel_imo']}")
    print("Make sure your server is running and test data is valid!\n")

    tester = ManualTester()

    while True:
        print("Choose a test:")
        print("1. Health check (owners endpoint)")
        print("2. Basic async test (heavy + quick endpoints)")
        print("3. Multiple heavy endpoints test")
        print("4. Load test (10 concurrent requests)")
        print("5. Heavy load test (50 concurrent requests)")
        print("6. Test individual endpoints")
        print("7. Quick response test (vessels)")
        print("0. Exit")

        choice = input("\nEnter your choice (0-7): ").strip()

        if choice == "0":
            print("Goodbye!")
            break
        elif choice == "1":
            tester.test_health()
        elif choice == "2":
            tester.test_basic_async()
        elif choice == "3":
            tester.test_multiple_heavy_endpoints()
        elif choice == "4":
            tester.test_load(10)
        elif choice == "5":
            tester.test_load(50)
        elif choice == "6":
            tester.test_individual_endpoints()
        elif choice == "7":
            tester.make_request("/vessels", "QUICK")
        else:
            print("Invalid choice. Please try again.")

        print("\n" + "="*50 + "\n")


if __name__ == "__main__":
    main()
