import logging
import sys
import traceback
import pytz
import pandas as pd
from datetime import date, datetime
from websockets.exceptions import ConnectionClosed, ConnectionClosedOK

from app.modules.efficiency.logic_helpers.efficiency_helper import EfficiencyHelper

sys.path.append("../..")
from app.general_logic_helper.utils.interchange_formats import InterchangeFormats
from app.general_logic_helper.handlers.data_handler import DataHandler
from app.general_logic_helper.config.configuration_helper import Configuration<PERSON>elper
from app.general_logic_helper.helpers.excel.excel_helper import ExcelHelper
from app.services.redis_api.redis_api_client import RedisApi


async def calculate_fleet_efficiency(request_data: dict):
    if not request_data:
        return {
            "status_code": 400,
            "status": "ERROR",
            "message": "No data provided.",
            "data": {},
        }
    efficiency_helper = EfficiencyHelper()
    filters = request_data.get("filters", {})
    request = request_data.get("request", {})

    fleet_efficiency = []
    vessels = filters.get("vessels")
    for vessel in vessels:
        _vessel_imo = vessel.get("imo")
        _vessel_id = vessel.get("id")
        _vessel_name = vessel.get("name")
        _assigned_owner_vat = vessel.get("assigned_owner_vat")
        request["vessel_imo"] = _vessel_imo
        request["vessel_name"] = _vessel_name
        request["vessel_id"] = _vessel_id
        request["selected_owner_vat"] = _assigned_owner_vat
        redis_data = await efficiency_helper.separate_data_by_load_condition(request)
        efficiency_data = await efficiency_helper.filter_data(
            filters, request, redis_data
        )
        fleet_efficiency.append({str(_vessel_imo): efficiency_data})
    return fleet_efficiency


class DataController:
    def __init__(self):
        self._config = ConfigurationHelper().instance()
        self.current_year = datetime.now(pytz.UTC).year
        self.redis_api = RedisApi()
        self.data_handler = DataHandler()
        self.boiler_add_groups = [
            20,
            23,
            25,
            28,
        ]  # Alice Theresa, Tina Theresa, Annelise Theresa, Karina Theresa

    def set_busy(self):
        """Sets the value server to busy state."""
        self._is_busy = True

    def set_idle(self):
        """Sets the value server to idle state."""
        self._is_busy = False

    def is_busy(self) -> bool:
        """Returns whether the server is currently busy."""
        return self._is_busy

    @staticmethod
    def json_serial(obj):
        if isinstance(obj, (datetime, date)):
            return datetime.strftime(obj, "%Y-%m-%d %H:%M:%S")
        raise TypeError("Type %s not serializable" % type(obj))

    async def wss_serve(self, redis_data, parameters):
        try:
            _from = parameters.get("from", None)
            _to = parameters.get("to", None)
            _sorted_type = parameters.get("sorted_type", None)
            self._filter = parameters.get("filter", None)
            _selected_owner_vat = parameters.get("selected_owner_vat", None)
            _vessel_imo = parameters.get("vessel_imo", None)
            _vessel_id = parameters.get("vessel_id", None)
            _vessel_name = parameters.get("vessel_name", None)
            _date_diff = parameters.get("date_diff", None)
            _monthly_fuel_report = parameters.get("monthly_fuel_report", None)
            _db_request = parameters.get("db_request", None)
            _exclude_anomalies = parameters.get("exclude_anomalies", None)

            log_speed_reliable = (
                await self.redis_api.get_key_data(
                    f"vessel_{_vessel_imo}_{_selected_owner_vat}:log_speed_reliable"
                )
            )[0].get("value")

            self.data_handler.generate_monthly_fuel(redis_data, _vessel_id)
            await self.data_handler.init_summary_helper(redis_data)
            if redis_data.get("measurements") and len(redis_data["measurements"]) > 0:
                self.convert_measurements_to_ic_format(redis_data, vessel_id=_vessel_id)
                self.data_handler.init_donut_helper(
                    redis_data, _vessel_name, log_speed_reliable, _vessel_imo
                )
            else:
                redis_data["measurements"] = redis_data.get("latest_row")
            logging.debug(
                "Fetching data for client %s - regular submit", _selected_owner_vat
            )
            logging.debug(
                "%s characters of JSON data for client %s",
                len(redis_data),
                _selected_owner_vat,
            )
            logging.info("Sending data to client")
            return redis_data
        except (ConnectionClosed, ConnectionClosedOK) as e:
            logging.info("Connection closed: %s", e)
            print(traceback.format_exc())

    def build_request_for_timeseries(self, parameters, type):
        return {
            "vessel_imo": parameters.get("vessel_imo"),
            "selected_owner_vat": parameters.get("selected_owner_vat"),
            "from_date": parameters.get("from_date"),
            "to_date": parameters.get("to_date"),
            "type": type,
        }

    async def get_time_series_data(self, type, parameters):
        time_series_request = self.build_request_for_timeseries(
            parameters=parameters, type=type
        )
        time_series_data = await self.redis_api.retrieve_time_series_data(time_series_request)
        time_series_data = time_series_data.get(type, []) if time_series_data else []
        time_series_data_df = pd.DataFrame(time_series_data)
        if not time_series_data_df.empty and "timestamp" in time_series_data_df.columns:
            time_series_data_df["timestamp"] = (
                time_series_data_df["timestamp"].astype(str).str.replace("T", " ")
            )
        return time_series_data_df

    async def wss_serve_data_analytics(self, redis_data, parameters):
        try:
            _from = parameters.get("from_date", None)
            _to = parameters.get("to_date", None)
            _selected_owner_vat = parameters.get("selected_owner_vat", None)
            _vessel_imo = parameters.get("vessel_imo", None)
            _vessel_id = parameters.get("vessel_id", None)

            if redis_data.get("measurements") and len(redis_data["measurements"]) > 0:
                # Get reliability of Log Speed sensor for vessel
                log_speed_reliable = (
                    await self.redis_api.get_key_data(
                        f"vessel_{_vessel_imo}_{_selected_owner_vat}:log_speed_reliable"
                    )
                )[0].get("value")

                self.convert_measurements_to_ic_format(redis_data, vessel_id=_vessel_id)
                self.data_handler.init_pie_helper(
                    redis_data, _vessel_imo, log_speed_reliable
                )
                self.build_async_data(redis_data, _vessel_id)

                # Get AIS data from time series Redis endpoint
                ais_data_df = await self.get_time_series_data("ais", parameters)
                # Get weather data from time series Redis endpoint
                weather_data_df = await self.get_time_series_data("weather", parameters)

                self.data_handler.init_data_analytics_helper(
                    redis_data,
                    log_speed_reliable,
                    ais_data_df,
                    weather_data_df,
                    _vessel_id,
                    _vessel_imo
                )
                redis_data.pop("measurements")

            redis_data["data_send_date"] = datetime.now(pytz.utc).strftime(
                "%Y-%m-%d %H:%M:%S"
            )

            logging.debug(
                "Fetching data for client %s - regular submit", _selected_owner_vat
            )
            logging.debug(
                "%s characters of JSON data for client %s",
                len(redis_data),
                _selected_owner_vat,
            )
            logging.info("Sending data to client")
            return redis_data
        except (ConnectionClosed, ConnectionClosedOK) as e:
            logging.info("Connection closed: %s", e)
            print(traceback.format_exc())

    def convert_measurements_to_ic_format(self, redis_data, vessel_id=None):
        measurements = redis_data.get("measurements")
        df = pd.DataFrame(measurements)
        df["timestamp"] = pd.to_datetime(df["timestamp"])
        if not df.empty:
            df.loc[df["me_1_fc_mass"] >= 50000.0, "me_1_fc_mass"] = 0
            measurements = df.to_dict(orient="records")
            for elem in measurements:
                elem["timestamp"] = elem["timestamp"].to_pydatetime()
        redis_data["measurements"] = InterchangeFormats.convert_to_ic_format(
            measurements, vessel_id=vessel_id
        )

    def build_async_data(self, vessel_data, vessel_id):
        logging.debug("Building async data for vessel: %s", vessel_id)
        # Instantiate necessary variables
        vessel_imo = vessel_data.get("imo")
        vessel_data_length = len(vessel_data["measurements"]) - 1
        boiler_add_groups = vessel_id in self.boiler_add_groups
        if vessel_id != 4:
            # For every vessel, calculate the boiler consumption
            for index, element in enumerate(vessel_data["measurements"]):
                if index <= vessel_data_length:
                    if index + 1 <= vessel_data_length:
                        ExcelHelper.start_boiler_net_calculation(
                            vessel_data["measurements"],
                            boiler_add_groups,
                            element,
                            index,
                            vessel_id,
                        )
                    if isinstance(vessel_data["measurements"][index]["boifcm"], str):
                        vessel_data["measurements"][index]["boifcm"] = 0.0

                if vessel_imo in [9392183, 9748710]:  # Sigrid Theresa, Sigaia Theresa
                    # ME Consumption Custom Calculation = ME Flow – (AUX Flow IN – AUX Flow OUT).
                    mefcm = element.get("mefcm")
                    auxfcm = element.get("auxfcm")
                    if (
                        mefcm is not None
                        and isinstance(mefcm, (float, int))
                        and auxfcm is not None
                        and isinstance(auxfcm, (float, int))
                    ):
                        element["mefcm"] = mefcm - auxfcm

                if (
                    vessel_imo == 9173290
                    or vessel_imo == 8918461
                    or vessel_imo == 9178460
                    or vessel_imo == 9192612
                ):
                    # If Wilson vessels, set mefcm to 0.0 if fc is under 50. Requested by ship owner.
                    if element.get("mefcm") < 50:
                        element["mefcm"] = 0.0

    async def calculate_efficiency_reports(self, request_data: dict):
        if not request_data:
            return {
                "status_code": 400,
                "status": "ERROR",
                "message": "No data provided.",
                "data": {},
            }
        efficiency_helper = EfficiencyHelper()
        filters = request_data.get("filters", {})
        request = request_data.get("request", {})

        if filters.get("type") == "fleet":
            return await calculate_fleet_efficiency(request_data)
        else:
            redis_data = await efficiency_helper.separate_data_by_load_condition(
                request
            )
        if not redis_data:
            return {
                "status_code": 404,
                "status": "ERROR",
                "message": "No data could be retrieved from Redis.",
                "data": {},
            }

        efficiencies_daily = await efficiency_helper.filter_data(
            filters, request, redis_data
        )

        vessel_key = f"vessel_{request['vessel_imo']}_{request['selected_owner_vat']}"
        efficiencies_monthly = await self.redis_api.get_key_data(
            f"{vessel_key}:reports:efficiencies_monthly", format="single"
        )
        speed_levels_log_30 = await self.redis_api.get_key_data(
            f"{vessel_key}:reports:speed_levels_log:speed_levels_log_30",
            format="single",
        )

        if not efficiencies_monthly:
            logging.error("No monthly efficiency data could be retrieved from Redis.")
            efficiencies_monthly = {}

        if not efficiencies_daily:
            return {
                "status_code": 404,
                "status": "ERROR",
                "message": "No efficiency data could be calculated.",
                "data": {},
            }

        report = {
            "reports": {
                "efficiencies_daily": {
                    "fuel_efficiency": efficiencies_daily[0],
                    "propulsion_efficiency": efficiencies_daily[1],
                    "speed_type": efficiencies_daily[2],
                },
                "efficiencies_monthly": efficiencies_monthly,
                "speed_levels_log": {
                    "speed_levels_log_30": speed_levels_log_30,
                },
            }
        }
        return report
