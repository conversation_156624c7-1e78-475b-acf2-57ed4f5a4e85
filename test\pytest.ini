[tool:pytest]
# Test discovery
testpaths = test
python_files = test_*.py *_test.py
python_classes = Test*
python_functions = test_*

# Async support
asyncio_mode = auto

# Coverage settings
addopts = 
    --strict-markers
    --strict-config
    --verbose
    --tb=short
    --cov=app
    --cov-report=html:test/reports/coverage
    --cov-report=term-missing
    --cov-report=xml:test/reports/coverage.xml
    --html=test/reports/report.html
    --self-contained-html
    --timeout=300

# Markers
markers =
    unit: Unit tests
    integration: Integration tests
    system: System/End-to-end tests
    slow: Slow running tests
    auth: Authentication related tests
    redis: Redis integration tests
    api: API endpoint tests
    concurrent: Concurrent/async tests
    load: Load testing
    smoke: Smoke tests

# Minimum coverage percentage
fail_under = 80

# Ignore warnings
filterwarnings =
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
    ignore::UserWarning:requests.*
