# For more information, please refer to https://aka.ms/vscode-docker-python
FROM python:3.10-slim

EXPOSE 8767

# To speed up pip install
RUN pip install --upgrade pip

# Keeps Python from generating .pyc files in the container
ENV PYTHONDONTWRITEBYTECODE=1

# Turns off buffering for easier container logging
ENV PYTHONUNBUFFERED=1


RUN apt-get update && \
    apt-get -y install gcc mono-mcs && \
    apt-get -y install libmariadb3 libmariadb-dev

# Copy the config and log files
COPY app/config/frugaltech.config /etc/frugaltech.config
COPY app/config/frugaltech.log /var/log/frugaltech.log

# Install git for later use
RUN apt-get update && apt-get install -y git

# Install pip requirements
COPY app/config/requirements.txt .
RUN python -m pip --no-cache-dir install --upgrade -r requirements.txt --force

WORKDIR /app

COPY api_helpers/ ./api_helpers/
COPY base_classes/ ./base_classes/
COPY common/ ./common/
COPY helpers/ ./helpers/
COPY redis_api/ ./
COPY tasks/ ./tasks/
COPY app/config/.env ./
COPY __init__.py ./
COPY auth.py ./
COPY flask_server.py ./

# Workers specifices the number of worker processes to launch and manage. For now 4 worker processes are specified.
# Each worker is its own instance of the application that allows the application to handle multiple requests concurrently by utilizing multiple CPU cores.
# The max requests is the maximum number of requests a worker will process before restarting.
CMD ["gunicorn", "-w", "4", "-k", "gthread", "flask_server:app", "--bind", "0.0.0.0:8767", "--max-requests", "5", "-t","300","--access-logfile", "-", "--keep-alive", "30"]
