# Export Logic - handles business logic for data export operations

from app.modules.export.logic_helpers.generate_file import GenerateFile
from app.modules.export.export_service import ExportService
from app.context.utils import rename_vat


class ExportLogic:
    # Logic layer for data export operations

    def __init__(self, export_service: ExportService, generate_file: GenerateFile):
        self.export_service = export_service
        self.generate_file = generate_file

    async def export_json(
        self,
        vessel_imo: str,
        owner_vat: str,
        from_date: str,
        to_date: str,
    ):
        # Generate JSON export for vessel data
        return await self.generate_file.generate_json(rename_vat(**locals()))

    async def export_excel(
        self,
        vessel_imo: str,
        owner_vat: str,
        from_date: str,
        to_date: str,
        vessel_id: int,
        vessel_name: str,
        sorted_type: bool,
        exclude_anomalies: bool,
    ):
        # Generate Excel export with sorting and anomaly filtering options
        return await self.generate_file.generate_excel(rename_vat(**locals()))

    async def get_cylinder_to(self, vessel_imo: str, owner_vat: str):
        # Get cylinder to date - return empty list if None
        return await self.export_service.get_cylinder_to(rename_vat(**locals())) or []

    async def get_cylinder_from(self, vessel_imo: str, owner_vat: str):
        # Get cylinder from date - return empty list if None
        return await self.export_service.get_cylinder_from(rename_vat(**locals())) or []

    async def export_cylinder(self, vessel_imo: str, owner_vat: str, from_date: str, to_date: str):
        # Generate JSON export for cylinder data
        return await self.generate_file.generate_cylinder_json(rename_vat(**locals()))

