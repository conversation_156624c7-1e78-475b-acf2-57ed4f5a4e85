#!/usr/bin/env python3
"""
Test runner script for the FastAPI backend application.
Provides convenient commands for running different types of tests.
"""

import argparse
import subprocess
import sys
import os
from pathlib import Path


def run_command(command, description):
    """Run a command and handle the output."""
    print(f"\n{'='*60}")
    print(f"Running: {description}")
    print(f"Command: {' '.join(command)}")
    print(f"{'='*60}")
    
    try:
        result = subprocess.run(command, check=True, capture_output=False)
        print(f"\n✅ {description} completed successfully!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"\n❌ {description} failed with exit code {e.returncode}")
        return False


def run_unit_tests(coverage=False, verbose=False):
    """Run unit tests."""
    command = ["pytest", "test/unit/", "-m", "unit"]
    
    if coverage:
        command.extend(["--cov=app", "--cov-report=html", "--cov-report=term"])
    
    if verbose:
        command.append("-v")
    
    return run_command(command, "Unit Tests")


def run_integration_tests(verbose=False):
    """Run integration tests."""
    command = ["pytest", "test/integration/", "-m", "integration"]
    
    if verbose:
        command.append("-v")
    
    return run_command(command, "Integration Tests")


def run_system_tests(verbose=False):
    """Run system tests."""
    command = ["pytest", "test/system/", "-m", "system"]
    
    if verbose:
        command.append("-v")
    
    return run_command(command, "System Tests")


def run_performance_tests(verbose=False):
    """Run performance tests."""
    command = ["pytest", "test/system/", "-m", "performance"]
    
    if verbose:
        command.append("-v")
    
    return run_command(command, "Performance Tests")


def run_load_tests(verbose=False):
    """Run load tests."""
    command = ["pytest", "test/system/", "-m", "load"]
    
    if verbose:
        command.append("-v")
    
    return run_command(command, "Load Tests")


def run_stress_tests(verbose=False):
    """Run stress tests."""
    command = ["pytest", "test/system/", "-m", "stress"]
    
    if verbose:
        command.append("-v")
    
    return run_command(command, "Stress Tests")


def run_all_tests(coverage=False, verbose=False, parallel=False):
    """Run all tests in sequence."""
    command = ["pytest"]
    
    if coverage:
        command.extend(["--cov=app", "--cov-report=html", "--cov-report=term"])
    
    if verbose:
        command.append("-v")
    
    if parallel:
        command.extend(["-n", "auto"])
    
    return run_command(command, "All Tests")


def run_quick_tests(verbose=False):
    """Run quick tests (unit + basic integration)."""
    success = True
    
    # Run unit tests
    success &= run_unit_tests(verbose=verbose)
    
    # Run basic integration tests (exclude performance/load tests)
    command = ["pytest", "test/integration/", "-m", "integration and not performance and not load"]
    if verbose:
        command.append("-v")
    
    success &= run_command(command, "Quick Integration Tests")
    
    return success


def run_ci_tests():
    """Run tests suitable for CI environment."""
    command = [
        "pytest",
        "--cov=app",
        "--cov-report=xml",
        "--cov-report=term",
        "--junitxml=test-results.xml",
        "-v"
    ]
    
    return run_command(command, "CI Tests")


def run_specific_test(test_path, verbose=False):
    """Run a specific test file or test method."""
    command = ["pytest", test_path]
    
    if verbose:
        command.append("-v")
    
    return run_command(command, f"Specific Test: {test_path}")


def check_test_environment():
    """Check if the test environment is properly set up."""
    print("Checking test environment...")
    
    # Check if we're in the right directory
    if not os.path.exists("test"):
        print("❌ Error: 'test' directory not found. Run this script from the project root.")
        return False
    
    # Check if test requirements are installed
    try:
        import pytest
        import httpx
        import coverage
        print("✅ Test dependencies are installed")
    except ImportError as e:
        print(f"❌ Error: Missing test dependency: {e}")
        print("Run: pip install -r test/requirements.txt")
        return False
    
    # Check if pytest.ini exists
    if not os.path.exists("pytest.ini"):
        print("⚠️  Warning: pytest.ini not found. Tests may not run with optimal configuration.")
    else:
        print("✅ pytest.ini found")
    
    return True


def main():
    """Main function to handle command line arguments."""
    parser = argparse.ArgumentParser(
        description="Test runner for FastAPI backend application",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python scripts/run_tests.py --all                    # Run all tests
  python scripts/run_tests.py --unit --coverage        # Run unit tests with coverage
  python scripts/run_tests.py --integration --verbose  # Run integration tests with verbose output
  python scripts/run_tests.py --performance            # Run performance tests
  python scripts/run_tests.py --quick                  # Run quick test suite
  python scripts/run_tests.py --ci                     # Run CI test suite
  python scripts/run_tests.py --specific test/unit/test_auth.py  # Run specific test file
        """
    )
    
    # Test type arguments
    parser.add_argument("--all", action="store_true", help="Run all tests")
    parser.add_argument("--unit", action="store_true", help="Run unit tests")
    parser.add_argument("--integration", action="store_true", help="Run integration tests")
    parser.add_argument("--system", action="store_true", help="Run system tests")
    parser.add_argument("--performance", action="store_true", help="Run performance tests")
    parser.add_argument("--load", action="store_true", help="Run load tests")
    parser.add_argument("--stress", action="store_true", help="Run stress tests")
    parser.add_argument("--quick", action="store_true", help="Run quick test suite (unit + basic integration)")
    parser.add_argument("--ci", action="store_true", help="Run CI test suite")
    parser.add_argument("--specific", type=str, help="Run specific test file or method")
    
    # Options
    parser.add_argument("--coverage", action="store_true", help="Generate coverage report")
    parser.add_argument("--verbose", "-v", action="store_true", help="Verbose output")
    parser.add_argument("--parallel", action="store_true", help="Run tests in parallel")
    parser.add_argument("--check-env", action="store_true", help="Check test environment setup")
    
    args = parser.parse_args()
    
    # Check environment if requested
    if args.check_env:
        if check_test_environment():
            print("✅ Test environment is properly configured")
            sys.exit(0)
        else:
            print("❌ Test environment has issues")
            sys.exit(1)
    
    # If no test type specified, show help
    if not any([args.all, args.unit, args.integration, args.system, 
                args.performance, args.load, args.stress, args.quick, 
                args.ci, args.specific]):
        parser.print_help()
        sys.exit(1)
    
    # Check environment before running tests
    if not check_test_environment():
        sys.exit(1)
    
    success = True
    
    # Run requested tests
    if args.specific:
        success = run_specific_test(args.specific, args.verbose)
    elif args.all:
        success = run_all_tests(args.coverage, args.verbose, args.parallel)
    elif args.quick:
        success = run_quick_tests(args.verbose)
    elif args.ci:
        success = run_ci_tests()
    else:
        # Run individual test types
        if args.unit:
            success &= run_unit_tests(args.coverage, args.verbose)
        
        if args.integration:
            success &= run_integration_tests(args.verbose)
        
        if args.system:
            success &= run_system_tests(args.verbose)
        
        if args.performance:
            success &= run_performance_tests(args.verbose)
        
        if args.load:
            success &= run_load_tests(args.verbose)
        
        if args.stress:
            success &= run_stress_tests(args.verbose)
    
    # Print final result
    print(f"\n{'='*60}")
    if success:
        print("🎉 All requested tests completed successfully!")
        sys.exit(0)
    else:
        print("💥 Some tests failed!")
        sys.exit(1)


if __name__ == "__main__":
    main()
