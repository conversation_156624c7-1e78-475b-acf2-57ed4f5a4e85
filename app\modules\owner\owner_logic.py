# Owner Logic - handles business logic for owner management operations

from app.modules.owner.owner_service import OwnerService
from app.context.utils import extract_kwargs


class OwnerLogic:
    # Logic layer for owner management operations

    def __init__(self, owner_service: OwnerService):
        self.owner_service = owner_service

    async def owners(self, session_owner_vat: str):
        # Get owners data - extract kwargs and call service
        return await self.owner_service.owners(**extract_kwargs(locals()))
