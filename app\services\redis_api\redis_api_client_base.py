import logging
import httpx
from typing import Optional, Dict, Any
from httpx import AsyncClient

from app.services.redis_api.redis_api_client_error import RedisApiError
from app.utils.singleton import Singleton
from .redis_api_client_config import redis_client_config


def _parse_json(resp: httpx.Response, method: str, path: str) -> Dict[str, Any]:
    """
    Parses the JSON response from the HTTP request.
    
    Args:
        resp: The HTTP response object
        method: The HTTP method used (GET, POST, etc.)
        path: The request path
    
    Raises:
        RedisApiError: If JSON parsing fails
    """
    try:
        return resp.json()
    except ValueError as e:
        logging.error("Invalid JSON from %s %s: %s", method, path, e)
        raise RedisApiError("Invalid JSON response from upstream") from e


def _validate_logical_status(data: Dict[str, Any], method: str, path: str) -> None:
    """
    Validates that the response contains a successful status.
    
    Args:
        data: The parsed JSON response
        method: The HTTP method used
        path: The request path
    
    Raises:
        RedisApiError: If status is not "SUCCESS"
    """
    if data.get("status") != "SUCCESS":
        msg = data.get("message", "Unknown error")
        logging.error("Logical failure %s %s: %s", method, path, msg)
        raise RedisApiError(msg, status_code=502)


def _extract_key(
    data: Dict[str, Any],
    key: Optional[str],
    method: str,
    path: str,
) -> Any:
    """
    Extracts a specific key from the response data.
    
    Args:
        data: The parsed JSON response
        key: The key to extract (optional)
        method: The HTTP method used
        path: The request path
    
    Returns:
        The value associated with the key, or the entire data if no key specified
    
    Raises:
        RedisApiError: If the specified key is missing
    """
    if key is None:
        return data

    try:
        return data[key]
    except KeyError:
        logging.error("Missing key %r in response from %s %s", key, method, path)
        raise RedisApiError(f"Malformed response, missing '{key}'")


def _validate_http_status(
    resp: httpx.Response,
    expected: int,
    method: str,
    path: str,
) -> None:
    """
    Validates that the HTTP response status matches the expected status.
    
    Args:
        resp: The HTTP response object
        expected: The expected HTTP status code
        method: The HTTP method used
        path: The request path
    
    Raises:
        RedisApiError: If status code doesn't match expected
    """
    if resp.status_code != expected:
        logging.error("Unexpected HTTP %s from %s %s", resp.status_code, method, path)
        raise RedisApiError(
            f"Upstream HTTP {resp.status_code}",
            status_code=resp.status_code
        )


class BaseRedisClient(metaclass=Singleton):
    """
    Base class for any Redis-backed HTTP client.
    Implements the Singleton pattern to ensure only one client instance exists.
    Handles HTTP requests to a Redis API with authentication and error handling.
    """

    def __init__(
        self,
        api_url: Optional[str] = None,
        api_token: Optional[str] = None,
        timeout: Optional[int] = None,
    ):
        """
        Initialize the Redis client with connection details.
        
        Args:
            api_url: Redis API base URL (optional, falls back to config)
            api_token: Authentication token (optional, falls back to config)
            timeout: Request timeout in seconds (optional, falls back to config)
        """
        # fall back to redis_api_client_config.py settings
        self.api_url = api_url or redis_client_config.redis_api_url
        self.api_token = api_token or redis_client_config.redis_api_token
        self.timeout = timeout or redis_client_config.redis_timeout

        # Ensure URL has http/https prefix
        if not self.api_url.startswith("http"):
            self.api_url = f"http://{self.api_url}"

        # Set default headers for all requests
        self.headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self.api_token}",
        }

        # Initialize the async HTTP client
        self.client = AsyncClient(
            base_url=self.api_url,
            headers=self.headers,
            timeout=self.timeout
        )

    async def call(
        self,
        path: str,
        *,
        method: str = "GET",
        json: Optional[Any] = None,
        data: Optional[Any] = None,
        key: Optional[str] = None,
        expected_status: int = 200,
        **params: Any,
    ) -> Any:
        """
        Make an HTTP request to the Redis API.
        
        Args:
            path: API endpoint path
            method: HTTP method (default: GET)
            json: JSON data to send (optional)
            data: Raw data to send (optional)
            key: Response key to extract (optional)
            expected_status: Expected HTTP status code (default: 200)
            **params: Query parameters
        
        Returns:
            Parsed response data
        
        Raises:
            ValueError: If both json and data are provided
            RedisApiError: For various HTTP/API errors
        """
        if json is not None and data is not None:
            raise ValueError("Cannot specify both `json` and `data` in the same request.")

        resp = await self._send_request(path, method, params, json, data)
        _validate_http_status(resp, expected_status, method, path)
        parsed = _parse_json(resp, method, path)
        _validate_logical_status(parsed, method, path)
        return _extract_key(parsed, key, method, path)

    async def _send_request(
        self,
        path: str,
        method: str,
        params: Dict[str, Any],
        json: Optional[Any],
        data: Optional[Any],
    ) -> httpx.Response:
        """
        Send the actual HTTP request.
        
        Args:
            path: API endpoint path
            method: HTTP method
            params: Query parameters
            json: JSON data to send (optional)
            data: Raw data to send (optional)
        
        Returns:
            HTTP response
        
        Raises:
            RedisApiError: On network errors
        """
        request_kwargs: Dict[str, Any] = {
            "method": method.upper(),
            "url": path,
            "params": params,
            "timeout": self.timeout,
        }

        # Only include json or data if they're not None
        if json is not None:
            request_kwargs["json"] = json
        if data is not None:
            request_kwargs["data"] = data

        try:
            return await self.client.request(**request_kwargs)
        except httpx.RequestError as e:
            logging.error("Network error %s %s: %s", method, path, e)
            raise RedisApiError("Network error fetching data") from e
