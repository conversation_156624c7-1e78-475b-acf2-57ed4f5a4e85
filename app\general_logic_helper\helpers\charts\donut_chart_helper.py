from datetime import datetime, timedelta

import pandas as pd

from app.general_logic_helper.helpers.charts.pie_chart_helper import Pie<PERSON>hartHelper


class DonutChartHelper:
    @staticmethod
    def seconds_to_hours_minutes(seconds):
        hours = seconds // 3600  # 1 hour = 3600 seconds
        minutes = (seconds % 3600) // 60  # 1 minute = 60 seconds
        return hours, minutes

    @staticmethod
    def init_data(redis_data, _vessel_name, log_speed_reliable, vessel_imo):
        # Update the start_time and end_time based on filtered measurements
        if redis_data["measurements"]:
            final_dict = None

            # Define the threshold for filtering
            seven_days_ago = datetime.now() - timedelta(days=7)

            # Convert JSON measurements to DataFrame
            df = pd.DataFrame(redis_data["measurements"])

            # Convert timestamp string to datetime
            df["timestamp"] = pd.to_datetime(df["timestamp"])

            # Filter measurements within the last 7 days
            filtered_df = df[df["timestamp"] >= seven_days_ago]

            if filtered_df["timestamp"].count() > 0:
                final_dict = PieChartHelper.init_data(
                    filtered_df,
                    log_speed_reliable,
                    is_donut_chart=True,
                    vessel_name=_vessel_name,
                    vessel_imo=vessel_imo
                )
            else:
                final_dict = {
                    "frugal_usage": "Data unavailable for past 7 days.",
                }
        else:
            final_dict = {
                "frugal_usage": "Data unavailable for past 7 days.",
            }

        redis_data["chartData"] = final_dict
