# Owner Service - handles data access for owner management operations

from app.services.redis_api.redis_api_client import RedisApi


class OwnerService:
    # Service layer for owner data operations

    def __init__(self, redis_client: RedisApi = None):
        # Initialize with Redis client (create new one if not provided)
        self.redis = redis_client or RedisApi()

    async def owners(self, **params):
        # Get owners data from Redis API
        return await self.redis.call(
            path="get_owners",
            key="result",
            **params
        )
