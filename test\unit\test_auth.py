"""
Unit tests for authentication module.
"""
import pytest
import jwt
from unittest.mock import AsyncM<PERSON>, patch, MagicMock
from fastapi import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Request

from auth import <PERSON><PERSON><PERSON><PERSON><PERSON>, managed_owners, validate_owner_access, owner_vat_check


class TestAuthHandler:
    """Test cases for AuthHandler class."""
    
    def test_init_with_valid_secret(self):
        """Test AuthHandler initialization with valid secret."""
        secret = "test-secret"
        handler = AuthHandler(secret)
        assert handler.secret_token == secret
    
    def test_init_with_empty_secret(self):
        """Test AuthHandler initialization with empty secret raises ValueError."""
        with pytest.raises(ValueError, match="JWT_SECRET must be set"):
            Auth<PERSON><PERSON><PERSON>("")
    
    def test_init_with_none_secret(self):
        """Test AuthHandler initialization with None secret raises ValueError."""
        with pytest.raises(ValueError, match="JWT_SECRET must be set"):
            <PERSON><PERSON><PERSON><PERSON><PERSON>(None)
    
    @pytest.mark.asyncio
    async def test_call_with_valid_token(self):
        """Test <PERSON>thHandler call with valid JWT token."""
        secret = "test-secret"
        handler = AuthHandler(secret)
        
        # Create valid token
        payload = {"user_id": "test_user"}
        token = jwt.encode(payload, secret, algorithm="HS256")
        
        # Mock request with valid token
        mock_request = MagicMock(spec=Request)
        mock_request.headers = {"X-API-Key": token}
        
        # Should not raise exception
        result = await handler(mock_request)
        assert result is None
    
    @pytest.mark.asyncio
    async def test_call_with_missing_token(self):
        """Test AuthHandler call with missing token raises HTTPException."""
        handler = AuthHandler("test-secret")
        
        # Mock request without token
        mock_request = MagicMock(spec=Request)
        mock_request.headers = {}
        
        with pytest.raises(HTTPException) as exc_info:
            await handler(mock_request)
        
        assert exc_info.value.status_code == 401
        assert "App token is missing" in exc_info.value.detail
    
    @pytest.mark.asyncio
    async def test_call_with_invalid_token(self):
        """Test AuthHandler call with invalid token raises HTTPException."""
        handler = AuthHandler("test-secret")
        
        # Mock request with invalid token
        mock_request = MagicMock(spec=Request)
        mock_request.headers = {"X-API-Key": "invalid-token"}
        
        with pytest.raises(HTTPException) as exc_info:
            await handler(mock_request)
        
        assert exc_info.value.status_code == 401
        assert "Invalid app token" in exc_info.value.detail
    
    @pytest.mark.asyncio
    async def test_call_with_expired_token(self):
        """Test AuthHandler call with expired token raises HTTPException."""
        secret = "test-secret"
        handler = AuthHandler(secret)
        
        # Create expired token
        payload = {"user_id": "test_user", "exp": 1}  # Expired timestamp
        token = jwt.encode(payload, secret, algorithm="HS256")
        
        # Mock request with expired token
        mock_request = MagicMock(spec=Request)
        mock_request.headers = {"X-API-Key": token}
        
        with pytest.raises(HTTPException) as exc_info:
            await handler(mock_request)
        
        assert exc_info.value.status_code == 401
        assert "Invalid app token" in exc_info.value.detail


class TestManagedOwners:
    """Test cases for managed_owners function."""
    
    @pytest.mark.asyncio
    async def test_managed_owners_success(self, mock_redis_client):
        """Test managed_owners with successful Redis response."""
        # Mock Redis response
        mock_redis_client.get_key_data.return_value = [
            {"value": [{"vat": "NO123456789", "name": "Test Owner"}]}
        ]
        
        with patch('auth.RedisApi', return_value=mock_redis_client):
            result = await managed_owners("NO950700939")
            
            assert result == [{"vat": "NO123456789", "name": "Test Owner"}]
            mock_redis_client.get_key_data.assert_called_once_with(
                "owner_NO950700939:managed_owners"
            )
    
    @pytest.mark.asyncio
    async def test_managed_owners_empty_response(self, mock_redis_client):
        """Test managed_owners with empty Redis response raises HTTPException."""
        # Mock empty Redis response
        mock_redis_client.get_key_data.return_value = [{"value": []}]
        
        with patch('auth.RedisApi', return_value=mock_redis_client):
            with pytest.raises(HTTPException) as exc_info:
                await managed_owners("NO950700939")
            
            assert exc_info.value.status_code == 404
            assert "No managed owners found" in exc_info.value.detail
    
    @pytest.mark.asyncio
    async def test_managed_owners_no_value_key(self, mock_redis_client):
        """Test managed_owners with Redis response missing value key."""
        # Mock Redis response without value key
        mock_redis_client.get_key_data.return_value = [{}]
        
        with patch('auth.RedisApi', return_value=mock_redis_client):
            with pytest.raises(HTTPException) as exc_info:
                await managed_owners("NO950700939")
            
            assert exc_info.value.status_code == 404


class TestValidateOwnerAccess:
    """Test cases for validate_owner_access function."""
    
    @pytest.mark.asyncio
    async def test_validate_owner_access_success(self):
        """Test validate_owner_access with valid access."""
        mock_managed = [
            {"vat": "NO123456789", "name": "Owner 1"},
            {"vat": "NO987654321", "name": "Owner 2"}
        ]
        
        with patch('auth.managed_owners', return_value=mock_managed):
            # Should not raise exception
            result = await validate_owner_access("NO123456789", "NO950700939")
            assert result is None
    
    @pytest.mark.asyncio
    async def test_validate_owner_access_missing_vat(self):
        """Test validate_owner_access with missing VAT raises HTTPException."""
        with pytest.raises(HTTPException) as exc_info:
            await validate_owner_access("", "NO950700939")
        
        assert exc_info.value.status_code == 422
        assert "Missing owner_vat or X-Tenant-Owner-Vat" in exc_info.value.detail
    
    @pytest.mark.asyncio
    async def test_validate_owner_access_missing_session_vat(self):
        """Test validate_owner_access with missing session VAT raises HTTPException."""
        with pytest.raises(HTTPException) as exc_info:
            await validate_owner_access("NO123456789", "")
        
        assert exc_info.value.status_code == 422
        assert "Missing owner_vat or X-Tenant-Owner-Vat" in exc_info.value.detail
    
    @pytest.mark.asyncio
    async def test_validate_owner_access_forbidden(self):
        """Test validate_owner_access with unauthorized VAT raises HTTPException."""
        mock_managed = [
            {"vat": "NO123456789", "name": "Owner 1"}
        ]
        
        with patch('auth.managed_owners', return_value=mock_managed):
            with pytest.raises(HTTPException) as exc_info:
                await validate_owner_access("NO999999999", "NO950700939")
            
            assert exc_info.value.status_code == 403
            assert "Forbidden" in exc_info.value.detail


class TestOwnerVatCheck:
    """Test cases for owner_vat_check function."""
    
    @pytest.mark.asyncio
    async def test_owner_vat_check_from_query_params(self):
        """Test owner_vat_check extracting VAT from query parameters."""
        mock_request = MagicMock(spec=Request)
        mock_request.query_params = {"owner_vat": "NO123456789"}
        
        with patch('auth.validate_owner_access', return_value=None):
            result = await owner_vat_check(mock_request, "NO950700939")
            assert result == "NO123456789"
    
    @pytest.mark.asyncio
    async def test_owner_vat_check_from_json_body(self):
        """Test owner_vat_check extracting VAT from JSON body."""
        mock_request = MagicMock(spec=Request)
        mock_request.query_params = {}
        mock_request.json = AsyncMock(return_value={"owner_vat": "NO123456789"})
        
        with patch('auth.validate_owner_access', return_value=None):
            result = await owner_vat_check(mock_request, "NO950700939")
            assert result == "NO123456789"
    
    @pytest.mark.asyncio
    async def test_owner_vat_check_missing_vat(self):
        """Test owner_vat_check with missing VAT raises HTTPException."""
        mock_request = MagicMock(spec=Request)
        mock_request.query_params = {}
        mock_request.json = AsyncMock(return_value={})
        
        with pytest.raises(HTTPException) as exc_info:
            await owner_vat_check(mock_request, "NO950700939")
        
        assert exc_info.value.status_code == 422
        assert "Missing owner_vat" in exc_info.value.detail
    
    @pytest.mark.asyncio
    async def test_owner_vat_check_invalid_json(self):
        """Test owner_vat_check with invalid JSON raises HTTPException."""
        mock_request = MagicMock(spec=Request)
        mock_request.query_params = {}
        mock_request.json = AsyncMock(side_effect=Exception("Invalid JSON"))
        
        with pytest.raises(HTTPException) as exc_info:
            await owner_vat_check(mock_request, "NO950700939")
        
        assert exc_info.value.status_code == 400
        assert "Invalid JSON body" in exc_info.value.detail
    
    @pytest.mark.asyncio
    async def test_owner_vat_check_validation_failure(self):
        """Test owner_vat_check with validation failure."""
        mock_request = MagicMock(spec=Request)
        mock_request.query_params = {"owner_vat": "NO123456789"}
        
        with patch('auth.validate_owner_access', side_effect=HTTPException(403, "Forbidden")):
            with pytest.raises(HTTPException) as exc_info:
                await owner_vat_check(mock_request, "NO950700939")
            
            assert exc_info.value.status_code == 403
