"""
Unit tests for context system.
"""
import pytest
import inspect
from unittest.mock import Magic<PERSON><PERSON>, AsyncMock, patch
from fastapi import Request, Depends

from app.context.context import get_context, get_dependencies


# Mock classes for testing
class MockLogic:
    """Mock logic class for testing."""
    
    def __init__(self, service=None, helper=None):
        self.service = service
        self.helper = helper
    
    async def test_method(self, param1: str, param2: int = 10):
        """Mock method for testing parameter extraction."""
        return {"param1": param1, "param2": param2}
    
    async def another_method(self, data: dict):
        """Another mock method for testing."""
        return {"data": data}


class MockService:
    """Mock service class for testing."""
    
    def __init__(self):
        self.name = "MockService"


class MockHelper:
    """Mock helper class for testing."""
    
    def __init__(self):
        self.name = "MockHelper"


class TestGetDependencies:
    """Test cases for get_dependencies function."""
    
    def test_get_dependencies_logic_only(self):
        """Test get_dependencies with logic class only."""
        get_logic = get_dependencies(MockLogic)
        
        # Should return a callable
        assert callable(get_logic)
        
        # Call it to get logic instance
        logic_instance = get_logic()
        assert isinstance(logic_instance, MockLogic)
        assert logic_instance.service is None
    
    def test_get_dependencies_with_service(self):
        """Test get_dependencies with logic and service classes."""
        get_logic = get_dependencies(MockLogic, MockService)
        
        logic_instance = get_logic()
        assert isinstance(logic_instance, MockLogic)
        assert isinstance(logic_instance.service, MockService)
    
    def test_get_dependencies_with_helpers(self):
        """Test get_dependencies with logic, service, and helper classes."""
        get_logic = get_dependencies(MockLogic, MockService, MockHelper)
        
        logic_instance = get_logic()
        assert isinstance(logic_instance, MockLogic)
        assert isinstance(logic_instance.service, MockService)
        assert hasattr(logic_instance, 'helper')
    
    def test_get_dependencies_no_service_with_helpers(self):
        """Test get_dependencies with logic and helpers but no service."""
        get_logic = get_dependencies(MockLogic, None, MockHelper)
        
        logic_instance = get_logic()
        assert isinstance(logic_instance, MockLogic)
        assert logic_instance.service is None
        assert hasattr(logic_instance, 'helper')


class TestGetContext:
    """Test cases for get_context function."""
    
    def test_get_context_returns_tuple(self):
        """Test that get_context returns a tuple of two functions."""
        result = get_context(MockLogic)
        
        assert isinstance(result, tuple)
        assert len(result) == 2
        
        get_params, get_logic = result
        assert callable(get_params)
        assert callable(get_logic)
    
    def test_get_context_with_service(self):
        """Test get_context with service class."""
        get_params, get_logic = get_context(MockLogic, MockService)
        
        # Test that get_logic returns correct instance
        logic_instance = get_logic()
        assert isinstance(logic_instance, MockLogic)
        assert isinstance(logic_instance.service, MockService)
    
    def test_get_context_with_helpers(self):
        """Test get_context with helper classes."""
        get_params, get_logic = get_context(MockLogic, MockService, MockHelper)
        
        logic_instance = get_logic()
        assert isinstance(logic_instance, MockLogic)
        assert isinstance(logic_instance.service, MockService)
    
    @pytest.mark.asyncio
    async def test_get_context_params_function(self):
        """Test the params function returned by get_context."""
        get_params, get_logic = get_context(MockLogic)
        
        # Create mock request
        mock_request = MagicMock(spec=Request)
        mock_request.method = "GET"
        mock_request.url.path = "/test"
        mock_request.query_params = {"param1": "test_value", "param2": "20"}
        mock_request.scope = {"endpoint": MagicMock()}
        
        # Create mock logic instance
        mock_logic = MockLogic()
        
        # Test parameter extraction
        with patch('app.context.context.determine_method_name', return_value='test_method'):
            params = await get_params(mock_request, logic=mock_logic)
            
            # Should return a dictionary
            assert isinstance(params, dict)


class TestContextIntegration:
    """Integration tests for context system."""
    
    @pytest.mark.asyncio
    async def test_context_with_real_request_simulation(self):
        """Test context system with simulated real request."""
        get_params, get_logic = get_context(MockLogic, MockService)
        
        # Create realistic mock request
        mock_request = MagicMock(spec=Request)
        mock_request.method = "POST"
        mock_request.url.path = "/api/test"
        mock_request.query_params = {}
        mock_request.headers = {"content-type": "application/json"}
        
        # Mock endpoint with configuration
        mock_endpoint = MagicMock()
        mock_endpoint.include_body = True
        mock_endpoint.headers_to_include = ["X-Tenant-Owner-Vat"]
        mock_request.scope = {"endpoint": mock_endpoint}
        
        # Get logic instance
        logic_instance = get_logic()
        
        # Mock the method determination and signature inspection
        with patch('app.context.context.determine_method_name', return_value='test_method'), \
             patch.object(logic_instance, 'test_method') as mock_method:
            
            # Set up method signature
            mock_method.__name__ = 'test_method'
            sig = inspect.Signature([
                inspect.Parameter('self', inspect.Parameter.POSITIONAL_OR_KEYWORD),
                inspect.Parameter('param1', inspect.Parameter.POSITIONAL_OR_KEYWORD, annotation=str),
                inspect.Parameter('param2', inspect.Parameter.POSITIONAL_OR_KEYWORD, annotation=int, default=10)
            ])
            
            with patch('inspect.signature', return_value=sig):
                # This should not raise an exception
                params = await get_params(mock_request, logic=logic_instance)
                assert isinstance(params, dict)
    
    def test_context_dependency_injection(self):
        """Test that context system properly injects dependencies."""
        get_params, get_logic = get_context(MockLogic, MockService, MockHelper)
        
        # Get multiple instances
        logic1 = get_logic()
        logic2 = get_logic()
        
        # Should be different instances (not singleton)
        assert logic1 is not logic2
        
        # But should have same types of dependencies
        assert type(logic1.service) == type(logic2.service)
        assert isinstance(logic1.service, MockService)
        assert isinstance(logic2.service, MockService)
    
    def test_context_type_annotations(self):
        """Test that context functions have proper type annotations."""
        get_params, get_logic = get_context(MockLogic, MockService)
        
        # Check get_logic return type
        logic_instance = get_logic()
        assert isinstance(logic_instance, MockLogic)
        
        # Check that get_params is async
        assert inspect.iscoroutinefunction(get_params)


class TestContextErrorHandling:
    """Test error handling in context system."""
    
    def test_get_context_with_invalid_logic_class(self):
        """Test get_context with invalid logic class."""
        # This should work but might cause issues when instantiated
        get_params, get_logic = get_context(str)  # Using str as invalid logic class
        
        # Should still return functions
        assert callable(get_params)
        assert callable(get_logic)
    
    @pytest.mark.asyncio
    async def test_context_params_with_missing_method(self):
        """Test parameter extraction when method doesn't exist."""
        get_params, get_logic = get_context(MockLogic)
        
        mock_request = MagicMock(spec=Request)
        mock_request.scope = {"endpoint": MagicMock()}
        logic_instance = MockLogic()
        
        with patch('app.context.context.determine_method_name', return_value='nonexistent_method'):
            # Should raise AttributeError when trying to get method signature
            with pytest.raises(AttributeError):
                await get_params(mock_request, logic=logic_instance)
    
    def test_context_with_none_service(self):
        """Test context system with None service class."""
        get_params, get_logic = get_context(MockLogic, None)
        
        logic_instance = get_logic()
        assert isinstance(logic_instance, MockLogic)
        assert logic_instance.service is None


class TestContextUtilities:
    """Test context system utility functions."""
    
    def test_context_function_signatures(self):
        """Test that context functions have expected signatures."""
        get_params, get_logic = get_context(MockLogic, MockService)
        
        # Check get_params signature
        params_sig = inspect.signature(get_params)
        assert 'request' in params_sig.parameters
        assert 'logic' in params_sig.parameters
        
        # Check get_logic signature
        logic_sig = inspect.signature(get_logic)
        # get_logic should have no required parameters (all dependencies injected)
        required_params = [p for p in logic_sig.parameters.values() 
                          if p.default == inspect.Parameter.empty]
        assert len(required_params) == 0
    
    def test_context_with_multiple_helpers(self):
        """Test context system with multiple helper classes."""
        class AnotherHelper:
            def __init__(self):
                self.name = "AnotherHelper"
        
        get_params, get_logic = get_context(MockLogic, MockService, MockHelper, AnotherHelper)
        
        logic_instance = get_logic()
        assert isinstance(logic_instance, MockLogic)
        assert isinstance(logic_instance.service, MockService)
        # Should have both helpers injected somehow
        assert hasattr(logic_instance, 'helper') or hasattr(logic_instance, 'anotherhelper')
