# FastAPI Async Testing with Real Endpoints

This directory contains comprehensive async testing scripts that test your FastAPI application using **real production endpoints** instead of synthetic test routes.

## 🎯 What These Tests Do

The tests verify that your FastAPI application properly handles asynchronous requests without blocking, specifically testing:

1. **Non-blocking behavior**: Heavy endpoints don't block quick endpoints
2. **Concurrent processing**: Multiple heavy endpoints can run simultaneously
3. **Load handling**: Server performance under various load conditions
4. **Rate limiting needs**: Identify if you need rate limiting (DDOS simulation)

## 📁 Files Overview

- **`test_config.py`** - Configuration file with authentication and test data
- **`setup_test_config.py`** - Interactive setup script for configuration
- **`test_concurrent_requests.py`** - Automated async functionality tests
- **`manual_test_commands.py`** - Interactive manual testing interface
- **`ddos_load_test.py`** - Heavy load testing and DDOS simulation

## 🚀 Quick Start

### 1. Set Up Configuration

Run the interactive setup script:

```bash
python setup_test_config.py
```

This will guide you through configuring:
- JWT secret key
- Owner VAT and session owner VAT
- Vessel IMO and vessel ID
- Date ranges with actual data
- Server URL

### 2. Run Tests

**Automated Testing:**
```bash
python test_concurrent_requests.py
```

**Manual Testing:**
```bash
python manual_test_commands.py
```

**Load Testing:**
```bash
python ddos_load_test.py
```

## ⚙️ Configuration Details

### Required Configuration

Update `test_config.py` with your actual test data:

- `GET /async-test/health` - Health check
- `GET /async-test/quick` - Returns immediately with random data
- `GET /async-test/long-sleep` - Sleeps for 12 seconds
- `GET /async-test/medium-sleep` - Sleeps for 10 seconds  
- `GET /async-test/very-long-sleep` - Sleeps for 25 seconds
- `GET /async-test/load-test` - Optimized for load testing
- `GET /async-test/custom-sleep?sleep_seconds=N` - Custom sleep duration (1-60s)

## 🧪 Running the Tests

### 1. Automated Comprehensive Testing

Run the full test suite with detailed analysis:

```bash
cd test/async_test_scripts
python test_concurrent_requests.py
```

This script runs three main tests:
- **Test 1**: Basic async functionality (long sleep + quick responses)
- **Test 2**: Multiple sleep durations (25s vs 10s)
- **Test 3**: Load testing (20 concurrent requests)

### 2. Manual Interactive Testing

For step-by-step manual testing:

```bash
cd test/async_test_scripts
python manual_test_commands.py
```

This provides an interactive menu to run individual tests and see real-time results.

### 3. Heavy Load Testing (DDOS Simulation)

Test server performance under heavy load:

```bash
cd test/async_test_scripts

# Basic load test
python ddos_load_test.py --requests 100 --concurrent 50

# Heavy load test
python ddos_load_test.py --requests 500 --concurrent 100

# DDOS simulation
python ddos_load_test.py --requests 1000 --concurrent 200

# Run predefined test suite
python ddos_load_test.py predefined
```

## 📊 Expected Results

### ✅ Correct Async Behavior

1. **Quick endpoints respond immediately** even when long-running endpoints are active
2. **Multiple concurrent requests** complete independently based on their duration
3. **Server handles reasonable load** without timeouts or crashes
4. **Response times remain consistent** under moderate load

### ❌ Signs of Problems

1. **Quick endpoints wait** for long endpoints to complete (blocking behavior)
2. **Requests timeout** under moderate load
3. **High error rates** during load testing
4. **Exponentially increasing response times** under load

## 🔧 Test Scenarios

### Scenario 1: Basic Async Verification

**What it tests**: Basic async functionality
**How to run**: 
```bash
# In one terminal/browser tab
curl http://localhost:8769/async-test/long-sleep

# Immediately in another terminal/browser tab
curl http://localhost:8769/async-test/quick
```

**Expected**: Quick endpoint returns immediately (~0.1s) while long endpoint is still running

### Scenario 2: Multiple Duration Testing

**What it tests**: Concurrent requests with different durations
**How to run**:
```bash
# Start very long sleep (25s)
curl http://localhost:8769/async-test/very-long-sleep &

# Start medium sleep (10s) twice
curl http://localhost:8769/async-test/medium-sleep &
curl http://localhost:8769/async-test/medium-sleep &
```

**Expected**: Both medium sleep requests complete (~10s each) before very long sleep completes (~25s)

### Scenario 3: Load Testing

**What it tests**: Server performance under concurrent load
**How to run**: Use the load testing scripts
**Expected**: 
- Success rate > 95%
- Average response time < 2s
- No server crashes or timeouts

## 🛠️ Troubleshooting

### Server Not Responding

1. Check if FastAPI server is running: `curl http://localhost:8769/async-test/health`
2. Verify port configuration in your `.env` file
3. Check server logs for errors

### High Error Rates in Load Tests

1. **Timeouts**: Server may be overwhelmed - consider implementing rate limiting
2. **Connection errors**: Increase connection limits or reduce concurrent requests
3. **Memory issues**: Monitor server memory usage during tests

### Blocking Behavior

If quick endpoints wait for long endpoints:
1. Verify you're using `async def` for endpoint functions
2. Check that you're using `await` for async operations
3. Ensure uvicorn is running with proper async configuration

## 📈 Performance Benchmarks

### Good Performance Indicators

- **Success rate**: > 99% for moderate load (< 100 concurrent)
- **Response time**: < 1s average for load test endpoint
- **Throughput**: > 50 requests/second
- **No blocking**: Quick endpoints always respond in < 0.5s

### When to Implement Rate Limiting

Consider rate limiting if you see:
- Success rate < 90% under load
- Response times > 5s consistently
- Server crashes or becomes unresponsive
- Memory usage grows unbounded

## 🔍 Monitoring During Tests

Watch these metrics while running tests:

1. **Server CPU usage**: Should remain reasonable (< 80%)
2. **Memory usage**: Should not grow unbounded
3. **Response times**: Should remain consistent
4. **Error logs**: Check for any exceptions or warnings

## 📝 Test Results Interpretation

The test scripts provide detailed output including:
- **Response times** for each request
- **Success/failure rates**
- **Performance percentiles**
- **Error categorization**
- **Recommendations** for optimization

Use these results to:
1. Verify async functionality works correctly
2. Determine optimal server configuration
3. Decide if rate limiting is needed
4. Plan for production load capacity
