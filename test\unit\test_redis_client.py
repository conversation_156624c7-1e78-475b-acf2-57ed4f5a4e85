"""
Unit tests for Redis API client.
"""
import pytest
from unittest.mock import AsyncMock, patch, MagicMock

from app.services.redis_api.redis_api_client import RedisApi
from app.services.redis_api.redis_api_client_error import RedisApiError


class TestRedisApi:
    """Test cases for RedisApi class."""
    
    def test_redis_api_singleton(self):
        """Test that <PERSON>is<PERSON><PERSON> implements singleton pattern."""
        # Clear any existing instances
        if hasattr(RedisApi, '_instances'):
            RedisApi._instances.clear()
        
        client1 = RedisApi()
        client2 = RedisApi()
        
        assert client1 is client2
    
    @pytest.mark.asyncio
    async def test_get_key_data_success(self):
        """Test get_key_data with successful response."""
        with patch.object(RedisApi, 'call') as mock_call:
            mock_call.return_value = [{"value": "test_data"}]
            
            client = RedisApi()
            result = await client.get_key_data("test_key")
            
            assert result == [{"value": "test_data"}]
            mock_call.assert_called_once_with(
                path="/redis/query?format=None",
                key="results",
                method="POST",
                json={"key": "test_key"}
            )
    
    @pytest.mark.asyncio
    async def test_get_key_data_with_format(self):
        """Test get_key_data with format parameter."""
        with patch.object(RedisApi, 'call') as mock_call:
            mock_call.return_value = [{"value": "formatted_data"}]
            
            client = RedisApi()
            result = await client.get_key_data("test_key", format="json")
            
            assert result == [{"value": "formatted_data"}]
            mock_call.assert_called_once_with(
                path="/redis/query?format=json",
                key="results",
                method="POST",
                json={"key": "test_key"}
            )
    
    @pytest.mark.asyncio
    async def test_get_key_data_error(self):
        """Test get_key_data with error response."""
        with patch.object(RedisApi, 'call') as mock_call:
            mock_call.side_effect = RedisApiError("Redis connection failed")
            
            client = RedisApi()
            
            with pytest.raises(RedisApiError, match="Redis connection failed"):
                await client.get_key_data("test_key")
    
    @pytest.mark.asyncio
    async def test_retrieve_time_series_data_success(self):
        """Test retrieve_time_series_data with successful response."""
        with patch.object(RedisApi, 'call') as mock_call:
            mock_call.return_value = {"data": [{"timestamp": "2024-01-01", "value": 100}]}
            
            client = RedisApi()
            request_data = {"vessel_imo": "8918461", "from_date": "2024-01-01"}
            result = await client.retrieve_time_series_data(request_data)
            
            assert result == {"data": [{"timestamp": "2024-01-01", "value": 100}]}
            mock_call.assert_called_once_with(
                path="get_time_series_data",
                method="POST",
                json=request_data
            )
    
    @pytest.mark.asyncio
    async def test_retrieve_time_series_data_no_request(self):
        """Test retrieve_time_series_data with no request parameter."""
        with patch.object(RedisApi, 'call') as mock_call:
            mock_call.return_value = {"data": []}
            
            client = RedisApi()
            result = await client.retrieve_time_series_data()
            
            assert result == {"data": []}
            mock_call.assert_called_once_with(
                path="get_time_series_data",
                method="POST",
                json=None
            )
    
    @pytest.mark.asyncio
    async def test_retrieve_time_series_data_error(self):
        """Test retrieve_time_series_data with error response."""
        with patch.object(RedisApi, 'call') as mock_call:
            mock_call.side_effect = RedisApiError("Time series data not found")
            
            client = RedisApi()
            
            with pytest.raises(RedisApiError, match="Time series data not found"):
                await client.retrieve_time_series_data({"vessel_imo": "invalid"})


class TestRedisApiIntegration:
    """Integration tests for RedisApi with base client."""
    
    @pytest.mark.asyncio
    async def test_redis_api_inherits_base_functionality(self):
        """Test that RedisApi properly inherits from BaseRedisClient."""
        # Test that RedisApi has the expected methods from base class
        client = RedisApi()
        
        # Should have call method from base class
        assert hasattr(client, 'call')
        assert callable(getattr(client, 'call'))
        
        # Should have specific methods
        assert hasattr(client, 'get_key_data')
        assert hasattr(client, 'retrieve_time_series_data')
    
    @pytest.mark.asyncio
    async def test_redis_api_error_handling(self):
        """Test RedisApi error handling."""
        with patch.object(RedisApi, 'call') as mock_call:
            # Test different types of errors
            error_scenarios = [
                Exception("Generic error"),
                RedisApiError("Redis specific error"),
                ConnectionError("Connection failed")
            ]
            
            client = RedisApi()
            
            for error in error_scenarios:
                mock_call.side_effect = error
                
                with pytest.raises(type(error)):
                    await client.get_key_data("test_key")
    
    def test_redis_api_singleton_thread_safety(self):
        """Test singleton behavior across multiple instantiations."""
        # Clear existing instances
        if hasattr(RedisApi, '_instances'):
            RedisApi._instances.clear()
        
        # Create multiple instances
        instances = [RedisApi() for _ in range(10)]
        
        # All should be the same instance
        first_instance = instances[0]
        for instance in instances[1:]:
            assert instance is first_instance
    
    @pytest.mark.asyncio
    async def test_redis_api_method_signatures(self):
        """Test that RedisApi methods have correct signatures."""
        client = RedisApi()
        
        # Test get_key_data signature
        import inspect
        sig = inspect.signature(client.get_key_data)
        params = list(sig.parameters.keys())
        assert 'path' in params
        assert 'format' in params
        
        # Test retrieve_time_series_data signature
        sig = inspect.signature(client.retrieve_time_series_data)
        params = list(sig.parameters.keys())
        assert 'request' in params


class TestRedisApiMocking:
    """Test RedisApi with various mocking scenarios."""
    
    @pytest.mark.asyncio
    async def test_redis_api_with_mock_responses(self, mock_redis_client):
        """Test RedisApi with predefined mock responses."""
        # Configure mock responses
        mock_redis_client.get_key_data.return_value = [{"value": "mocked_data"}]
        mock_redis_client.retrieve_time_series_data.return_value = {"data": "mocked_ts_data"}
        
        # Test get_key_data
        result = await mock_redis_client.get_key_data("test_key")
        assert result == [{"value": "mocked_data"}]
        
        # Test retrieve_time_series_data
        result = await mock_redis_client.retrieve_time_series_data({"test": "data"})
        assert result == {"data": "mocked_ts_data"}
    
    @pytest.mark.asyncio
    async def test_redis_api_call_counting(self):
        """Test that we can count Redis API calls for performance testing."""
        with patch.object(RedisApi, 'call') as mock_call:
            mock_call.return_value = {"status": "success"}
            
            client = RedisApi()
            
            # Make multiple calls
            await client.get_key_data("key1")
            await client.get_key_data("key2")
            await client.retrieve_time_series_data({"test": "data"})
            
            # Verify call count
            assert mock_call.call_count == 3
    
    @pytest.mark.asyncio
    async def test_redis_api_parameter_validation(self):
        """Test parameter validation in RedisApi methods."""
        with patch.object(RedisApi, 'call') as mock_call:
            mock_call.return_value = {"status": "success"}
            
            client = RedisApi()
            
            # Test with various parameter types
            await client.get_key_data("string_key")
            await client.get_key_data("key_with_special_chars!@#")
            
            # Verify calls were made with correct parameters
            calls = mock_call.call_args_list
            assert len(calls) == 2
            
            # Check first call
            assert calls[0][1]['json']['key'] == "string_key"
            # Check second call
            assert calls[1][1]['json']['key'] == "key_with_special_chars!@#"
