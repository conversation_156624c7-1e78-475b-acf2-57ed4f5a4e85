# User Service - handles data access for user management operations

from app.services.redis_api.redis_api_client import RedisApi


class UserService:
    # Service layer for user data operations

    def __init__(self, redis_client: RedisApi = None):
        # Initialize with Redis client (create new one if not provided)
        self.redis = redis_client or RedisApi()

    async def users_me(self, payload: dict):
        # Get current user data by email
        return await self.redis.call(
            path="get_user_by_email",
            method="POST",
            params=None,
            json=payload,
        )

    async def users(self, **params):
        # Get all users for an owner organization
        return await self.redis.call(
            path="get_user_info_by_owner",
            key="result",
            **params
        )
