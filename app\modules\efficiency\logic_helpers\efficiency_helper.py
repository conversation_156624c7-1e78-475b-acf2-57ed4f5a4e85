import logging
import math
import traceback
from bisect import bisect_left
from datetime import datetime, timedelta
from typing import List, Any, Optional

import pandas as pd
from pandas import DataFrame

from app.general_logic_helper.utils.interchange_formats import InterchangeFormats
from app.general_logic_helper.helpers.data.data_analytics_helper import DataAnalyticsHelper
from app.services.redis_api.redis_api_client import RedisApi


def remove_t_in_timestamp(timestamp):
    if "T" in timestamp:
        return timestamp.replace("T", " ")
    return timestamp


class EfficiencyHelper:
    def __init__(self):
        self.redis_api = RedisApi()

    BEAUFORT_SCALE = DataAnalyticsHelper.BEAUFORT_SCALE

    @staticmethod
    def find_closest_item(sorted_timestamps, target_timestamp):
        if isinstance(target_timestamp, str):
            target_timestamp = pd.to_datetime(target_timestamp)
        timestamps = [timestamp for timestamp, idx in sorted_timestamps]
        pos = bisect_left(timestamps, target_timestamp)
        if pos == 0:
            return sorted_timestamps[0][1]
        if pos == len(sorted_timestamps):
            return sorted_timestamps[-1][1]
        before = sorted_timestamps[pos - 1]
        after = sorted_timestamps[pos]
        if abs(after[0] - target_timestamp) < abs(before[0] - target_timestamp):
            return after[1]
        else:
            return before[1]

    @staticmethod
    def calculate_beaufort(wind_speed):
        rounded_wind_speed = round(wind_speed, 1)
        if rounded_wind_speed >= 32.7:
            return 12  # Hurricane level
        for beaufort in EfficiencyHelper.BEAUFORT_SCALE:
            if beaufort["min_speed"] <= rounded_wind_speed <= beaufort["max_speed"]:
                return beaufort["beaufort number"]

    @staticmethod
    def preprocess_weather_data(weather_data_df):
        weather_data_df["timestamp"] = pd.to_datetime(weather_data_df["timestamp"])
        weather_data_df["beaufort"] = weather_data_df["wind_speed"].apply(
            EfficiencyHelper.calculate_beaufort
        )
        weather_data_df.sort_values("timestamp", inplace=True)
        return list(
            weather_data_df[["timestamp", "beaufort"]].itertuples(
                index=False, name=None
            )
        )

    async def _get_vessel_measurements(
        self,
        request_data: dict,
    ) -> List[Any]:
        """Fetch and process vessel measurements.

        Args:
            request_data: dict containing request data

        Returns:
            List: List containing vessel measurements
        """
        request_data["type"] = "measurements"
        measurements_request = await self.redis_api.retrieve_time_series_data(request_data)
        measurements = (
            measurements_request.get("measurements", []) if measurements_request else []
        )

        if len(measurements) < 1:
            return []

        try:
            filtered_df = pd.DataFrame(measurements)
            filtered_df["timestamp"] = pd.to_datetime(filtered_df["timestamp"])
            if not filtered_df.empty:
                filtered_df.loc[
                    filtered_df["me_1_fc_mass"] >= 50000.0, "me_1_fc_mass"
                ] = 0
                measurements = filtered_df.to_dict(orient="records")
                for elem in measurements:
                    elem["timestamp"] = elem["timestamp"].to_pydatetime()

            ic_format = InterchangeFormats.convert_to_ic_format(measurements)
        except Exception as e:
            logging.error(f"Error processing vessel measurements: {e}")
            return []
        return ic_format

    async def get_log_speed_reliability(self, vessel_key):
        reliability = (
            await self.redis_api.get_key_data(f"{vessel_key}:log_speed_reliable")
        )[0].get("value")
        if reliability is None:
            return "lspd"

        if len(reliability) > 0 and reliability == "gps_speed":
            return "gspd"
        elif len(reliability) > 0 and reliability == "log_speed":
            return "lspd"
        else:
            return "gspd"

    @staticmethod
    def filter_by_fp_status(filtered_data, fp_on, fp_off):
        if (fp_on and fp_off) or (not fp_on and not fp_off):
            return filtered_data
        else:
            if fp_on and not fp_off:
                return [
                    element for element in filtered_data if element.get("fstat") >= 1
                ]
            if fp_off and not fp_on:
                return [
                    element for element in filtered_data if element.get("fstat") == 0
                ]

    @staticmethod
    def get_beaufort(weather_data_preprocessed, target_timestamp):
        max_length = len(weather_data_preprocessed)
        # Convert the target timestamp to a pandas Timestamp object
        target_timestamp = pd.to_datetime(target_timestamp)
        # Use bisect to find the closest timestamp
        index = bisect_left(weather_data_preprocessed, (target_timestamp,))
        # Get the Beaufort number for the closest timestamp
        _, beaufort = weather_data_preprocessed[
            min(index, len(weather_data_preprocessed) - 1)
        ]
        if index == max_length:
            index -= 1
        time_diff = abs(
            weather_data_preprocessed[index][0] - target_timestamp
        ).total_seconds()
        if time_diff > 1500:
            return 12
        return beaufort

    async def filter_data(self, filters, request, redis_data):
        filter_load_condition = filters.get("loadCondition")
        fp_on, fp_off = filters.get("fpOn"), filters.get("fpOff")
        beaufort = filters.get("beaufort")
        vessel_key = f"vessel_{request['vessel_imo']}_{request['selected_owner_vat']}"
        vessel_id = request.get("vessel_id")

        # Retrieve the time series data for the weather
        request["type"] = "weather"
        weather_request = await self.redis_api.retrieve_time_series_data(request)
        weather_data = weather_request.get("weather", []) if weather_request else []
        weather_df = DataFrame(weather_data)

        filtered_data = redis_data
        if len(weather_df) > 0:
            weather_df["timestamp"] = (
                weather_df["timestamp"].astype(str).apply(remove_t_in_timestamp)
            )
            weather_data = self.preprocess_weather_data(weather_df)
            # Filter for Beaufort
            filtered_data = [
                element
                for element in redis_data
                if (
                    beaufort_value := self.get_beaufort(
                        weather_data, element.get("timestamp")
                    )
                )
                is not None
                and beaufort_value <= beaufort
            ]
        # Filter for FP Status
        filtered_data = self.filter_by_fp_status(filtered_data, fp_on, fp_off)
        # Filter for Load Condition
        if filter_load_condition != "All":
            filtered_data = [
                element
                for element in filtered_data
                if (load_condition := element.get("load_condition")) is not None
                and load_condition == filter_load_condition
            ]
        if filters.get("type") == "vessel":
            return await self.calculate_efficiency(filtered_data, vessel_key, True)
        elif filters.get("type") == "fleet":
            return await self.calculate_efficiency_for_fleet(
                filtered_data, vessel_key, vessel_id
            )
        else:
            return []

    @staticmethod
    def convert_minutes_to_hours(minutes):
        hour = math.floor(minutes / 60)
        minutes = minutes % 60
        return hour, minutes

    @staticmethod
    def calculate_loading_percentage(actual_draft, max_draft):
        # Comparing with maximum draft
        status = "Laden"  # Assume the vessel is laden by default

        if actual_draft >= max_draft:
            return 100.0, status  # The vessel is fully laden
        else:
            loading_percentage = (actual_draft / max_draft) * 100.0
            if loading_percentage < 85:
                # Check if the actual draft is less than or equal to 62% of the max draft
                if 85.0 > loading_percentage > 66.0:
                    status = "Partially Laden"
                else:
                    status = "Ballast"

        return loading_percentage, status

    @staticmethod
    def validate_type(element, types):
        if isinstance(element, types):
            return True
        return False

    async def separate_data_by_load_condition(self, request: dict) -> List[Any]:
        data = []
        try:
            # Retrieve the measurement data for the vessel
            data = await self._get_vessel_measurements(request)
            vessel_imo = request.get("vessel_imo")
            selected_owner_vat = request.get("selected_owner_vat")
            # We manually construct the key manually for this single value
            latest_max_draft = await self.redis_api.get_key_data(
                f"vessel_{vessel_imo}_{selected_owner_vat}:max_draft", format="single"
            )
            if latest_max_draft:
                latest_max_draft = latest_max_draft[0].get("value")[0].get("value")
                if latest_max_draft != "None":
                    latest_max_draft = float(latest_max_draft)
                else:
                    latest_max_draft = None
            else:
                latest_max_draft = None

            # Retrieve the AIS data for the vessel
            request["type"] = "ais"
            ais_data = await self.redis_api.retrieve_time_series_data(request)

            ais_data_df = None
            timestamps = []

            if ais_data:
                ais_data = ais_data.get("ais", [])

                ais_data_df = pd.DataFrame(ais_data)
                if "timestamp" in ais_data_df.columns:
                    ais_data_df["timestamp"] = (
                        ais_data_df["timestamp"].astype(str).str.replace("T", " ")
                    )
                timestamps = sorted(
                    [
                        (pd.Timestamp(i["timestamp"]), index)
                        for index, i in enumerate(ais_data_df.to_dict("records"))
                    ]
                )

            special_imo_calculation = vessel_imo in [9392183, 9748710]

            for _, element in enumerate(data):
                # Handle special calculations for specific IMO numbers
                if special_imo_calculation:
                    mefcm = element.get("mefcm")  # Main Engine Fuel Consumption Mass
                    auxfcm = element.get(
                        "auxfcm"
                    )  # Auxiliary Engine Fuel Consumption Mass
                    if mefcm and auxfcm:
                        # These vessels have a special calculation for ME Consumption
                        element["mefcm"] = mefcm - auxfcm
                draft_aft = element.get("da")  # Draft Aft
                ais_draft: Optional[float] = None

                if draft_aft > 0:
                    ais_draft = draft_aft
                elif ais_data_df is not None and not ais_data_df.empty:
                    # Only calculate the AIS draft if the draft aft is not available or valid
                    # Find the closest AIS data point to the current measurement
                    closest_item_index = EfficiencyHelper.find_closest_item(
                        timestamps, pd.Timestamp(element.get("timestamp"))
                    )

                    # Calculate the time difference between the current measurement and the closest AIS data point
                    time_diff = (
                        pd.Timestamp(element.get("timestamp"))
                        - timestamps[closest_item_index][0]
                    ).total_seconds()

                    # Retrieve the AIS draft from the closest AIS data point
                    ais_draft = ais_data_df.at[closest_item_index, "draught"]

                    # If the time difference is too large, invalid data is found or the AIS draft is not available, skip this data point
                    if (
                        time_diff > 3600
                        or closest_item_index is None
                        or ais_draft is None
                    ):
                        continue

                # Update the measurement with the AIS draft values
                element["ais_draft"] = ais_draft

                if ais_draft is not None and isinstance(latest_max_draft, float):
                    ais_draft_float = float(ais_draft)

                    # Calculate the loading percentage and load condition status
                    _, status = EfficiencyHelper.calculate_loading_percentage(
                        ais_draft_float,
                        float(latest_max_draft),
                    )

                    # Update the measurement with the loading condition status
                    element["load_condition"] = status

        except Exception as e:
            logging.error(
                "Error while separating data by load condition: %s",
                traceback.format_exc(),
            )
        finally:
            return data

    async def calculate_efficiency(self, filtered_data, vessel_key, daily=False):
        fuel_efficiency = []
        propulsion_efficiency = []
        me_fc_speed = 0.00
        me_fc_power = 0.00
        power = 0.00
        speed_fuel = 0.00
        speed_propulsion = 0.00
        count_speed = 0
        count_power = 0
        minimum = 10
        length = len(filtered_data)
        speed_type = await self.get_log_speed_reliability(vessel_key)

        if length > 0:
            for index, measurement in enumerate(filtered_data):
                next_element = None
                unit = None
                next_unit = None
                sstat = measurement.get("sstat")
                if index + 1 <= length - 1:
                    next_element = filtered_data[index + 1]
                    if daily:
                        next_unit = next_element["timestamp"].day
                        unit = measurement["timestamp"].day
                    else:
                        next_unit = next_element["timestamp"].month
                        unit = measurement["timestamp"].month
                if next_element is not None and unit == next_unit:
                    fc = measurement["mefcm"]
                    if (
                        vessel_key == "vessel_9392183_DK40533516"
                        or vessel_key == "vessel_9748710_DK40533516"
                    ):
                        fc = measurement["mefcm"] - abs(measurement["auxfcm"])
                    if fc > minimum:
                        pwr = measurement["spow"]
                        spd = measurement[speed_type]
                        if pwr > minimum and sstat:
                            me_fc_power += fc
                            power += pwr
                            speed_fuel += spd
                            count_power += 1
                        if sstat:
                            me_fc_speed += fc
                            speed_propulsion += spd
                            count_speed += 1
                else:
                    me_fc_average_power = 0.00
                    time_fuel_efficiency = 0
                    average_power = 0.00
                    average_speed_fuel = 0.00
                    if power > minimum and me_fc_power > minimum and count_power > 0:
                        average_speed_fuel = speed_fuel / count_power
                        average_power = power / count_power
                        me_fc_average_power = me_fc_power / count_power
                        hours = math.floor(count_power / 60)
                        minutes = count_power % 60
                        time_fuel_efficiency = (
                            str(hours) + " hours and " + str(minutes) + " minutes"
                        )

                    me_fc_average_speed = 0.00
                    average_speed_propulsion = 0.00
                    time_propulsion_efficiency = 0
                    if (
                        speed_propulsion > 0
                        and me_fc_speed > minimum
                        and count_speed > 0
                    ):
                        average_speed_propulsion = speed_propulsion / count_speed
                        me_fc_average_speed = me_fc_speed / count_speed
                        hours, minutes = self.convert_minutes_to_hours(count_speed)
                        time_propulsion_efficiency = (
                            str(hours) + " hours and " + str(minutes) + " minutes"
                        )

                    date_str = (
                        datetime.strftime(measurement["timestamp"], "%Y-%m-%d 00:00:00")
                        if daily
                        else datetime.strftime(
                            measurement["timestamp"], "%Y-%m-01 00:00:00"
                        )
                    )
                    if average_power > minimum and me_fc_average_power > minimum:
                        fuel_efficiency.append(
                            {
                                date_str: {
                                    date_str: str(
                                        round(
                                            me_fc_average_power * 1000 / average_power,
                                            1,
                                        )
                                    ),
                                    "time": time_fuel_efficiency,
                                    "speed": round(average_speed_fuel, 2),
                                }
                            }
                        )
                    if average_speed_propulsion > 0 and me_fc_average_speed > minimum:
                        date_str = date_str[0:12] + str(timedelta(seconds=1))
                        propulsion_efficiency.append(
                            {
                                date_str: {
                                    date_str: str(
                                        round(
                                            me_fc_average_speed
                                            / average_speed_propulsion,
                                            1,
                                        )
                                    ),
                                    "time": time_propulsion_efficiency,
                                    "speed": round(average_speed_propulsion, 2),
                                }
                            }
                        )
                    me_fc_speed = 0.00
                    me_fc_power = 0.00
                    power = 0.00
                    speed_fuel = 0.00
                    speed_propulsion = 0.00
                    count_power = 0
                    count_speed = 0
        speed_type_to_return = "log_speed" if speed_type == "lspd" else "gps_speed"
        fuel_efficiency.reverse()
        propulsion_efficiency.reverse()
        return [fuel_efficiency, propulsion_efficiency, speed_type_to_return]

    async def calculate_efficiency_for_fleet(self, data, vessel_key, vessel_id):
        minimum = 10
        length = len(data)
        speed_type = await self.get_log_speed_reliability(vessel_key)

        avg_me_fc_fuel = 0
        avg_me_fc_propulsion = 0

        avg_shaft_pwr = 0

        avg_speed_fuel = 0
        avg_speed_propulsion = 0

        count_fuel = 0
        count_propulsion = 0

        vessel_efficiency = {"fuel": {}, "propulsion": {}}
        if length > 0:
            for index, measurement in enumerate(data):
                # Add the sstat check before processing measurements
                sstat = measurement.get("sstat")
                fc = measurement["mefcm"]
                if (
                    vessel_key == "vessel_9392183_DK40533516"
                    or vessel_key == "vessel_9748710_DK40533516"
                ):
                    fc = measurement["mefcm"] - abs(measurement["auxfcm"])
                if fc > minimum:
                    pwr = measurement["spow"]
                    spd = measurement[speed_type]
                    if sstat:
                        if pwr is not None and spd is not None and pwr > minimum:
                            avg_me_fc_fuel += fc
                            avg_shaft_pwr += pwr
                            avg_speed_fuel += spd
                            count_fuel += 1
                        avg_me_fc_propulsion += fc
                        avg_speed_propulsion += spd
                        count_propulsion += 1

            time_sailed_fuel = None
            if count_fuel > 0:
                avg_me_fc_fuel = avg_me_fc_fuel / count_fuel
                avg_speed_fuel = avg_speed_fuel / count_fuel
                avg_shaft_pwr = avg_shaft_pwr / count_fuel

                hours = math.floor(count_fuel / 60)
                minutes = count_fuel % 60
                time_sailed_fuel = (
                    str(hours) + " hours and " + str(minutes) + " minutes"
                )

            time_sailed_propulsion = None
            if count_propulsion > 0:
                avg_me_fc_propulsion = avg_me_fc_propulsion / count_propulsion
                avg_speed_propulsion = avg_speed_propulsion / count_propulsion

                hours = math.floor(count_propulsion / 60)
                minutes = count_propulsion % 60
                time_sailed_propulsion = (
                    str(hours) + " hours and " + str(minutes) + " minutes"
                )

            if (
                avg_me_fc_fuel > minimum
                and avg_speed_fuel > 0
                and avg_shaft_pwr > minimum
            ):
                vessel_efficiency["fuel"] = {
                    "efficiency": round(avg_me_fc_fuel * 1000 / avg_shaft_pwr, 1),
                    "speed": round(avg_speed_fuel, 2),
                    "hours_sailed": time_sailed_fuel,
                }

            if avg_me_fc_propulsion > minimum and avg_speed_propulsion > 0:
                vessel_efficiency["propulsion"] = {
                    "efficiency": round(avg_me_fc_propulsion / avg_speed_propulsion, 1),
                    "speed": round(avg_speed_propulsion, 2),
                    "hours_sailed": time_sailed_propulsion,
                }
        return vessel_efficiency
