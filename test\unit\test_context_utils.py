"""
Unit tests for context utility functions.
"""
import pytest
from app.context.utils import rename_vat, select_keys, rename_owner_vat, extract_kwargs


class TestRenameVat:
    """Test cases for rename_vat function."""
    
    def test_rename_vat_with_kwargs(self):
        """Test rename_vat with keyword arguments."""
        result = rename_vat(
            owner_vat="NO950700939",
            vessel_imo="8918461",
            from_date="2024-01-01"
        )
        
        expected = {
            "selected_owner_vat": "NO950700939",
            "vessel_imo": "8918461",
            "from_date": "2024-01-01"
        }
        assert result == expected
    
    def test_rename_vat_with_dict_arg(self):
        """Test rename_vat with dictionary argument."""
        input_dict = {
            "owner_vat": "NO950700939",
            "vessel_imo": "8918461",
            "from_date": "2024-01-01"
        }
        
        result = rename_vat(input_dict)
        
        expected = {
            "selected_owner_vat": "NO950700939",
            "vessel_imo": "8918461",
            "from_date": "2024-01-01"
        }
        assert result == expected
    
    def test_rename_vat_with_self_parameter(self):
        """Test rename_vat removes 'self' parameter."""
        input_dict = {
            "self": "some_object",
            "owner_vat": "NO950700939",
            "vessel_imo": "8918461"
        }
        
        result = rename_vat(input_dict)
        
        expected = {
            "selected_owner_vat": "NO950700939",
            "vessel_imo": "8918461"
        }
        assert result == expected
        assert "self" not in result
    
    def test_rename_vat_with_specific_keys(self):
        """Test rename_vat with specific key selection."""
        input_dict = {
            "owner_vat": "NO950700939",
            "vessel_imo": "8918461",
            "from_date": "2024-01-01",
            "extra_param": "should_be_excluded"
        }
        
        result = rename_vat(input_dict, "owner_vat", "vessel_imo")
        
        expected = {
            "selected_owner_vat": "NO950700939",
            "vessel_imo": "8918461"
        }
        assert result == expected
        assert "from_date" not in result
        assert "extra_param" not in result
    
    def test_rename_vat_no_owner_vat(self):
        """Test rename_vat when no owner_vat is present."""
        input_dict = {
            "vessel_imo": "8918461",
            "from_date": "2024-01-01"
        }
        
        result = rename_vat(input_dict)
        
        expected = {
            "vessel_imo": "8918461",
            "from_date": "2024-01-01"
        }
        assert result == expected
    
    def test_rename_vat_empty_dict(self):
        """Test rename_vat with empty dictionary."""
        result = rename_vat({})
        assert result == {}
    
    def test_rename_vat_mixed_args_and_kwargs(self):
        """Test rename_vat with both dict arg and kwargs."""
        input_dict = {"owner_vat": "NO950700939", "vessel_imo": "8918461"}
        
        # When both dict and kwargs are provided, kwargs take precedence
        result = rename_vat(from_date="2024-01-01", to_date="2024-01-07")
        
        expected = {
            "from_date": "2024-01-01",
            "to_date": "2024-01-07"
        }
        assert result == expected


class TestSelectKeys:
    """Test cases for select_keys function."""
    
    def test_select_keys_all_keys(self):
        """Test select_keys without specific key names (select all)."""
        source = {
            "owner_vat": "NO950700939",
            "vessel_imo": "8918461",
            "from_date": "2024-01-01"
        }
        
        result = select_keys(source)
        
        assert result == source
        assert result is not source  # Should be a copy
    
    def test_select_keys_specific_keys(self):
        """Test select_keys with specific key names."""
        source = {
            "owner_vat": "NO950700939",
            "vessel_imo": "8918461",
            "from_date": "2024-01-01",
            "extra_param": "excluded"
        }
        
        result = select_keys(source, "owner_vat", "vessel_imo")
        
        expected = {
            "owner_vat": "NO950700939",
            "vessel_imo": "8918461"
        }
        assert result == expected
    
    def test_select_keys_nonexistent_keys(self):
        """Test select_keys with non-existent key names."""
        source = {
            "owner_vat": "NO950700939",
            "vessel_imo": "8918461"
        }
        
        result = select_keys(source, "owner_vat", "nonexistent_key")
        
        expected = {"owner_vat": "NO950700939"}
        assert result == expected
    
    def test_select_keys_removes_self(self):
        """Test select_keys removes 'self' parameter."""
        source = {
            "self": "some_object",
            "owner_vat": "NO950700939",
            "vessel_imo": "8918461"
        }
        
        result = select_keys(source)
        
        expected = {
            "owner_vat": "NO950700939",
            "vessel_imo": "8918461"
        }
        assert result == expected
        assert "self" not in result
    
    def test_select_keys_empty_source(self):
        """Test select_keys with empty source dictionary."""
        result = select_keys({})
        assert result == {}
    
    def test_select_keys_empty_names(self):
        """Test select_keys with empty names list."""
        source = {"owner_vat": "NO950700939", "vessel_imo": "8918461"}
        result = select_keys(source, *[])
        
        # Should return all keys except 'self'
        assert result == source


class TestRenameOwnerVat:
    """Test cases for rename_owner_vat function."""
    
    def test_rename_owner_vat_with_owner_vat(self):
        """Test rename_owner_vat with owner_vat key."""
        params = {
            "owner_vat": "NO950700939",
            "vessel_imo": "8918461",
            "from_date": "2024-01-01"
        }
        
        result = rename_owner_vat(params)
        
        expected = {
            "selected_owner_vat": "NO950700939",
            "vessel_imo": "8918461",
            "from_date": "2024-01-01"
        }
        assert result == expected
    
    def test_rename_owner_vat_without_owner_vat(self):
        """Test rename_owner_vat without owner_vat key."""
        params = {
            "vessel_imo": "8918461",
            "from_date": "2024-01-01"
        }
        
        result = rename_owner_vat(params)
        
        assert result == params
        assert "selected_owner_vat" not in result
    
    def test_rename_owner_vat_with_selected_owner_vat(self):
        """Test rename_owner_vat with existing selected_owner_vat."""
        params = {
            "selected_owner_vat": "NO950700939",
            "vessel_imo": "8918461"
        }
        
        result = rename_owner_vat(params)
        
        # Should remain unchanged
        assert result == params
    
    def test_rename_owner_vat_empty_dict(self):
        """Test rename_owner_vat with empty dictionary."""
        result = rename_owner_vat({})
        assert result == {}
    
    def test_rename_owner_vat_preserves_other_keys(self):
        """Test rename_owner_vat preserves all other keys."""
        params = {
            "owner_vat": "NO950700939",
            "vessel_imo": "8918461",
            "from_date": "2024-01-01",
            "to_date": "2024-01-07",
            "vessel_id": 32,
            "complex_data": {"nested": "value"}
        }
        
        result = rename_owner_vat(params)
        
        expected = {
            "selected_owner_vat": "NO950700939",
            "vessel_imo": "8918461",
            "from_date": "2024-01-01",
            "to_date": "2024-01-07",
            "vessel_id": 32,
            "complex_data": {"nested": "value"}
        }
        assert result == expected


class TestExtractKwargs:
    """Test cases for extract_kwargs function."""
    
    def test_extract_kwargs_removes_self(self):
        """Test extract_kwargs removes 'self' parameter."""
        local_vars = {
            "self": "some_object",
            "param1": "value1",
            "param2": "value2"
        }
        
        result = extract_kwargs(local_vars)
        
        expected = {
            "param1": "value1",
            "param2": "value2"
        }
        assert result == expected
        assert "self" not in result
    
    def test_extract_kwargs_without_self(self):
        """Test extract_kwargs when no 'self' parameter exists."""
        local_vars = {
            "param1": "value1",
            "param2": "value2",
            "param3": "value3"
        }
        
        result = extract_kwargs(local_vars)
        
        assert result == local_vars
        assert result is not local_vars  # Should be a copy
    
    def test_extract_kwargs_empty_dict(self):
        """Test extract_kwargs with empty dictionary."""
        result = extract_kwargs({})
        assert result == {}
    
    def test_extract_kwargs_only_self(self):
        """Test extract_kwargs with only 'self' parameter."""
        local_vars = {"self": "some_object"}
        result = extract_kwargs(local_vars)
        assert result == {}
    
    def test_extract_kwargs_preserves_types(self):
        """Test extract_kwargs preserves parameter types."""
        local_vars = {
            "self": "some_object",
            "string_param": "test",
            "int_param": 42,
            "list_param": [1, 2, 3],
            "dict_param": {"key": "value"},
            "none_param": None,
            "bool_param": True
        }
        
        result = extract_kwargs(local_vars)
        
        expected = {
            "string_param": "test",
            "int_param": 42,
            "list_param": [1, 2, 3],
            "dict_param": {"key": "value"},
            "none_param": None,
            "bool_param": True
        }
        assert result == expected
        
        # Verify types are preserved
        assert isinstance(result["string_param"], str)
        assert isinstance(result["int_param"], int)
        assert isinstance(result["list_param"], list)
        assert isinstance(result["dict_param"], dict)
        assert result["none_param"] is None
        assert isinstance(result["bool_param"], bool)


class TestUtilityFunctionsIntegration:
    """Integration tests for utility functions working together."""
    
    def test_typical_usage_pattern(self):
        """Test typical usage pattern combining multiple utilities."""
        # Simulate a typical method call with locals()
        def mock_method(self, owner_vat, vessel_imo, from_date, extra_param):
            # This simulates what happens in a real method
            local_vars = locals()
            
            # Step 1: Extract kwargs (remove self)
            kwargs = extract_kwargs(local_vars)
            
            # Step 2: Select specific keys
            selected = select_keys(kwargs, "owner_vat", "vessel_imo", "from_date")
            
            # Step 3: Rename owner_vat
            renamed = rename_owner_vat(selected)
            
            return renamed
        
        # Execute
        result = mock_method(
            self="mock_self",
            owner_vat="NO950700939",
            vessel_imo="8918461",
            from_date="2024-01-01",
            extra_param="excluded"
        )
        
        # Assert
        expected = {
            "selected_owner_vat": "NO950700939",
            "vessel_imo": "8918461",
            "from_date": "2024-01-01"
        }
        assert result == expected
    
    def test_rename_vat_full_workflow(self):
        """Test rename_vat function which combines all utilities."""
        # Simulate method locals
        method_locals = {
            "self": "mock_self",
            "owner_vat": "NO950700939",
            "vessel_imo": "8918461",
            "from_date": "2024-01-01",
            "to_date": "2024-01-07",
            "extra_param": "should_be_excluded"
        }
        
        # Test with specific key selection
        result = rename_vat(method_locals, "owner_vat", "vessel_imo", "from_date")
        
        expected = {
            "selected_owner_vat": "NO950700939",
            "vessel_imo": "8918461",
            "from_date": "2024-01-01"
        }
        assert result == expected
        
        # Test with all keys
        result_all = rename_vat(**method_locals)
        
        expected_all = {
            "selected_owner_vat": "NO950700939",
            "vessel_imo": "8918461",
            "from_date": "2024-01-01",
            "to_date": "2024-01-07",
            "extra_param": "should_be_excluded"
        }
        assert result_all == expected_all
