# Anomaly Detection Router - API endpoints for vessel anomaly detection

from fastapi import APIRouter, Depends
from auth import owner_vat_check

from app.modules.anomaly_detection.anomaly_detection_logic import AnomalyDetectionLogic
from app.modules.anomaly_detection.anomaly_detection_service import AnomalyDetectionService
from app.context.context import get_context

# Create router with anomaly detection tag
router = APIRouter(tags=["Anomaly Detection"])

# Set up dependency injection for logic and service layers
anomaly_detection_params, get_anomaly_detection_logic = get_context(
    AnomalyDetectionLogic,
    AnomalyDetectionService,
)


@router.get("/anomaly-detection", dependencies=[Depends(owner_vat_check)])
async def anomaly_detection(
    params: dict = Depends(anomaly_detection_params),
    logic: AnomalyDetectionLogic = Depends(get_anomaly_detection_logic),
):
    # GET endpoint for anomaly detection - requires owner authentication
    return await logic.anomaly_detection(**params)
