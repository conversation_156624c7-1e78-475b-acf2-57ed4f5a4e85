"""
Performance and load testing for the FastAPI backend.
"""
import pytest
import asyncio
import time
import statistics
from unittest.mock import patch, AsyncMock
from httpx import AsyncClient

from test.utils.test_helpers import PerformanceTestHelper


@pytest.mark.system
@pytest.mark.performance
class TestAPIPerformance:
    """Performance tests for API endpoints."""
    
    @pytest.mark.asyncio
    async def test_quick_endpoint_performance(self, async_client: AsyncClient, performance_test_helper):
        """Test performance of quick response endpoints."""
        # Test the async-test/quick endpoint which should be very fast
        results = await performance_test_helper.measure_concurrent_requests(
            async_client,
            "/async-test/quick",
            num_requests=50,
            max_concurrent=10
        )
        
        # Performance assertions
        assert results["success_rate"] >= 0.95  # 95% success rate
        assert results["avg_response_time"] < 1.0  # Average under 1 second
        assert results["max_response_time"] < 3.0  # Max under 3 seconds
        assert results["min_response_time"] < 0.5  # Min under 0.5 seconds
    
    @pytest.mark.asyncio
    async def test_data_analytics_performance(self, async_client: AsyncClient, auth_headers, mock_auth_dependencies):
        """Test performance of data analytics endpoint under load."""
        payload = {
            "owner_vat": "NO950700939",
            "vessel_imo": "8918461",
            "vessel_id": 32,
            "vessel_name": "Wilson Saga",
            "from_date": "2024-01-01 00:00:00",
            "to_date": "2024-01-07 23:59:59"
        }
        
        with patch('app.services.redis_api.redis_api_client.aioredis') as mock_aioredis:
            mock_connection = AsyncMock()
            mock_aioredis.from_url.return_value = mock_connection
            mock_connection.get_key_data.return_value = [{"value": True}]
            
            # Mock analytics response
            mock_response = AsyncMock()
            mock_response.json.return_value = {"analytics": {"performance": "test"}}
            mock_response.status_code = 200
            
            with patch('httpx.AsyncClient.request', return_value=mock_response):
                # Measure performance of POST requests
                start_time = time.time()
                
                tasks = [
                    async_client.post("/data-analytics", json=payload, headers=auth_headers)
                    for _ in range(20)
                ]
                
                responses = await asyncio.gather(*tasks, return_exceptions=True)
                
                end_time = time.time()
                total_time = end_time - start_time
                
                # Analyze results
                successful_responses = [
                    r for r in responses 
                    if not isinstance(r, Exception) and hasattr(r, 'status_code') and r.status_code == 200
                ]
                
                # Performance assertions
                assert len(successful_responses) >= 18  # 90% success rate
                assert total_time < 30.0  # Complete within 30 seconds
                assert total_time / len(successful_responses) < 2.0  # Average under 2 seconds per request
    
    @pytest.mark.asyncio
    async def test_mixed_endpoint_load(self, async_client: AsyncClient, auth_headers, mock_auth_dependencies):
        """Test performance with mixed endpoint requests."""
        with patch('app.services.redis_api.redis_api_client.aioredis') as mock_aioredis:
            mock_connection = AsyncMock()
            mock_aioredis.from_url.return_value = mock_connection
            mock_connection.get_key_data.return_value = [{"value": True}]
            
            mock_response = AsyncMock()
            mock_response.json.return_value = {"mixed": "data"}
            mock_response.status_code = 200
            
            with patch('httpx.AsyncClient.request', return_value=mock_response):
                # Create mixed requests
                tasks = []
                
                # Quick endpoints (should be fast)
                for _ in range(10):
                    tasks.append(async_client.get("/async-test/quick"))
                
                # Authenticated endpoints (medium complexity)
                for _ in range(10):
                    tasks.append(async_client.get("/users?owner_vat=NO950700939", headers=auth_headers))
                
                # Analytics endpoints (high complexity)
                for _ in range(5):
                    payload = {
                        "owner_vat": "NO950700939",
                        "vessel_imo": "8918461",
                        "vessel_id": 32,
                        "vessel_name": "Wilson Saga",
                        "from_date": "2024-01-01 00:00:00",
                        "to_date": "2024-01-07 23:59:59"
                    }
                    tasks.append(async_client.post("/data-analytics", json=payload, headers=auth_headers))
                
                # Execute all requests concurrently
                start_time = time.time()
                responses = await asyncio.gather(*tasks, return_exceptions=True)
                end_time = time.time()
                
                total_time = end_time - start_time
                successful_responses = [
                    r for r in responses 
                    if not isinstance(r, Exception) and hasattr(r, 'status_code') and r.status_code < 400
                ]
                
                # Performance assertions
                assert len(successful_responses) >= 20  # 80% success rate
                assert total_time < 45.0  # Complete within 45 seconds


@pytest.mark.system
@pytest.mark.load
class TestLoadTesting:
    """Load testing for system capacity and limits."""
    
    @pytest.mark.asyncio
    async def test_high_concurrency_load(self, async_client: AsyncClient):
        """Test system behavior under high concurrency."""
        # Test with high number of concurrent quick requests
        num_requests = 100
        max_concurrent = 20
        
        async def make_request():
            try:
                response = await async_client.get("/async-test/quick")
                return response.status_code
            except Exception as e:
                return str(e)
        
        # Create semaphore to limit concurrency
        semaphore = asyncio.Semaphore(max_concurrent)
        
        async def limited_request():
            async with semaphore:
                return await make_request()
        
        # Execute load test
        start_time = time.time()
        results = await asyncio.gather(*[limited_request() for _ in range(num_requests)])
        end_time = time.time()
        
        # Analyze results
        successful_requests = [r for r in results if r == 200]
        failed_requests = [r for r in results if r != 200]
        
        total_time = end_time - start_time
        requests_per_second = num_requests / total_time
        
        # Load test assertions
        assert len(successful_requests) >= 80  # At least 80% success rate
        assert requests_per_second >= 10  # At least 10 requests per second
        assert total_time < 60.0  # Complete within 1 minute
        
        print(f"Load test results:")
        print(f"  Total requests: {num_requests}")
        print(f"  Successful: {len(successful_requests)}")
        print(f"  Failed: {len(failed_requests)}")
        print(f"  Total time: {total_time:.2f}s")
        print(f"  Requests/second: {requests_per_second:.2f}")
    
    @pytest.mark.asyncio
    async def test_sustained_load(self, async_client: AsyncClient):
        """Test system behavior under sustained load."""
        duration_seconds = 30  # Run for 30 seconds
        request_interval = 0.1  # Request every 100ms
        
        results = []
        start_time = time.time()
        
        while time.time() - start_time < duration_seconds:
            try:
                response = await async_client.get("/async-test/quick")
                results.append({
                    "timestamp": time.time(),
                    "status_code": response.status_code,
                    "response_time": response.elapsed.total_seconds() if hasattr(response, 'elapsed') else 0
                })
            except Exception as e:
                results.append({
                    "timestamp": time.time(),
                    "status_code": "error",
                    "error": str(e)
                })
            
            await asyncio.sleep(request_interval)
        
        # Analyze sustained load results
        successful_results = [r for r in results if r["status_code"] == 200]
        error_results = [r for r in results if r["status_code"] != 200]
        
        success_rate = len(successful_results) / len(results)
        
        # Sustained load assertions
        assert success_rate >= 0.85  # 85% success rate over sustained period
        assert len(results) >= 200  # Should have made at least 200 requests
        
        print(f"Sustained load results:")
        print(f"  Duration: {duration_seconds}s")
        print(f"  Total requests: {len(results)}")
        print(f"  Success rate: {success_rate:.2%}")
        print(f"  Errors: {len(error_results)}")
    
    @pytest.mark.asyncio
    async def test_memory_usage_under_load(self, async_client: AsyncClient):
        """Test memory usage patterns under load."""
        import psutil
        import os
        
        # Get initial memory usage
        process = psutil.Process(os.getpid())
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        # Generate load with large responses
        num_requests = 50
        
        tasks = [
            async_client.get("/async-test/quick")
            for _ in range(num_requests)
        ]
        
        await asyncio.gather(*tasks, return_exceptions=True)
        
        # Check memory usage after load
        final_memory = process.memory_info().rss / 1024 / 1024  # MB
        memory_increase = final_memory - initial_memory
        
        # Memory usage assertions
        assert memory_increase < 100  # Should not increase by more than 100MB
        
        print(f"Memory usage:")
        print(f"  Initial: {initial_memory:.2f} MB")
        print(f"  Final: {final_memory:.2f} MB")
        print(f"  Increase: {memory_increase:.2f} MB")


@pytest.mark.system
@pytest.mark.stress
class TestStressTesting:
    """Stress testing to find system breaking points."""
    
    @pytest.mark.asyncio
    async def test_connection_limit_stress(self, async_client: AsyncClient):
        """Test system behavior at connection limits."""
        # Gradually increase load until failure
        max_concurrent_levels = [10, 25, 50, 100, 200]
        
        for max_concurrent in max_concurrent_levels:
            print(f"Testing with {max_concurrent} concurrent connections...")
            
            semaphore = asyncio.Semaphore(max_concurrent)
            
            async def stress_request():
                async with semaphore:
                    try:
                        response = await async_client.get("/async-test/quick")
                        return response.status_code
                    except Exception as e:
                        return f"error: {str(e)}"
            
            # Test with current concurrency level
            start_time = time.time()
            results = await asyncio.gather(*[stress_request() for _ in range(max_concurrent * 2)])
            end_time = time.time()
            
            successful_requests = [r for r in results if r == 200]
            success_rate = len(successful_requests) / len(results)
            
            print(f"  Success rate: {success_rate:.2%}")
            print(f"  Time: {end_time - start_time:.2f}s")
            
            # If success rate drops below 50%, we've found the breaking point
            if success_rate < 0.5:
                print(f"Breaking point found at {max_concurrent} concurrent connections")
                break
            
            # Brief pause between stress levels
            await asyncio.sleep(1)
    
    @pytest.mark.asyncio
    async def test_request_size_stress(self, async_client: AsyncClient, auth_headers, mock_auth_dependencies):
        """Test system behavior with large request payloads."""
        with patch('app.services.redis_api.redis_api_client.aioredis') as mock_aioredis:
            mock_connection = AsyncMock()
            mock_aioredis.from_url.return_value = mock_connection
            mock_connection.get_key_data.return_value = [{"value": True}]
            
            mock_response = AsyncMock()
            mock_response.json.return_value = {"large": "response"}
            mock_response.status_code = 200
            
            with patch('httpx.AsyncClient.request', return_value=mock_response):
                # Test with increasingly large payloads
                payload_sizes = [1, 10, 100, 1000]  # KB
                
                for size_kb in payload_sizes:
                    # Create large payload
                    large_data = "x" * (size_kb * 1024)  # size_kb KB of data
                    payload = {
                        "owner_vat": "NO950700939",
                        "vessel_imo": "8918461",
                        "vessel_id": 32,
                        "vessel_name": "Wilson Saga",
                        "from_date": "2024-01-01 00:00:00",
                        "to_date": "2024-01-07 23:59:59",
                        "large_data": large_data
                    }
                    
                    try:
                        start_time = time.time()
                        response = await async_client.post(
                            "/data-analytics",
                            json=payload,
                            headers=auth_headers
                        )
                        end_time = time.time()
                        
                        response_time = end_time - start_time
                        
                        print(f"Payload size {size_kb}KB: {response.status_code}, {response_time:.2f}s")
                        
                        # Should handle reasonable payload sizes
                        if size_kb <= 100:  # Up to 100KB should work
                            assert response.status_code == 200
                            assert response_time < 10.0  # Should complete within 10 seconds
                        
                    except Exception as e:
                        print(f"Payload size {size_kb}KB failed: {str(e)}")
                        # Large payloads may fail, which is acceptable


@pytest.mark.system
@pytest.mark.benchmark
class TestBenchmarking:
    """Benchmarking tests for performance baselines."""
    
    @pytest.mark.asyncio
    async def test_endpoint_response_time_benchmarks(self, async_client: AsyncClient, auth_headers, mock_auth_dependencies):
        """Establish response time benchmarks for different endpoint types."""
        with patch('app.services.redis_api.redis_api_client.aioredis') as mock_aioredis:
            mock_connection = AsyncMock()
            mock_aioredis.from_url.return_value = mock_connection
            mock_connection.get_key_data.return_value = [{"value": True}]
            
            mock_response = AsyncMock()
            mock_response.json.return_value = {"benchmark": "data"}
            mock_response.status_code = 200
            
            with patch('httpx.AsyncClient.request', return_value=mock_response):
                benchmarks = {}
                
                # Benchmark different endpoint types
                endpoints = [
                    ("quick", "/async-test/quick", "GET", None),
                    ("health", "/async-test/health", "GET", None),
                    ("users", "/users?owner_vat=NO950700939", "GET", None),
                    ("vessels", "/vessels?owner_vat=NO950700939", "GET", None),
                    ("analytics", "/data-analytics", "POST", {
                        "owner_vat": "NO950700939",
                        "vessel_imo": "8918461",
                        "vessel_id": 32,
                        "vessel_name": "Wilson Saga",
                        "from_date": "2024-01-01 00:00:00",
                        "to_date": "2024-01-07 23:59:59"
                    })
                ]
                
                for name, url, method, payload in endpoints:
                    response_times = []
                    
                    # Run each endpoint 10 times
                    for _ in range(10):
                        start_time = time.time()
                        
                        if method == "GET":
                            if "async-test" in url:
                                response = await async_client.get(url)
                            else:
                                response = await async_client.get(url, headers=auth_headers)
                        else:  # POST
                            response = await async_client.post(url, json=payload, headers=auth_headers)
                        
                        end_time = time.time()
                        response_times.append(end_time - start_time)
                    
                    # Calculate statistics
                    benchmarks[name] = {
                        "avg": statistics.mean(response_times),
                        "min": min(response_times),
                        "max": max(response_times),
                        "median": statistics.median(response_times),
                        "std_dev": statistics.stdev(response_times) if len(response_times) > 1 else 0
                    }
                
                # Print benchmark results
                print("\nEndpoint Benchmarks:")
                for name, stats in benchmarks.items():
                    print(f"  {name}:")
                    print(f"    Average: {stats['avg']:.3f}s")
                    print(f"    Min: {stats['min']:.3f}s")
                    print(f"    Max: {stats['max']:.3f}s")
                    print(f"    Median: {stats['median']:.3f}s")
                    print(f"    Std Dev: {stats['std_dev']:.3f}s")
                
                # Benchmark assertions
                assert benchmarks["quick"]["avg"] < 0.5  # Quick endpoint should be very fast
                assert benchmarks["health"]["avg"] < 0.5  # Health check should be fast
                assert benchmarks["users"]["avg"] < 2.0  # User endpoint should be reasonable
                assert benchmarks["analytics"]["avg"] < 5.0  # Analytics can be slower but reasonable
