"""
Global test configuration and fixtures for the entire test suite.
"""
import asyncio
import os
import pytest
import jwt
from typing import As<PERSON><PERSON><PERSON>ator, Generator
from unittest.mock import AsyncMock, MagicMock, patch
from fastapi.testclient import TestClient
from httpx import AsyncClient

# Import application components
from run import create_api
from app.config.settings import Settings
from app.services.redis_api.redis_api_client import RedisApi
from auth import AuthHandler


@pytest.fixture(scope="session")
def event_loop():
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture(scope="session")
def test_settings():
    """Test settings configuration."""
    # Override settings for testing
    os.environ["JWT_SECRET"] = "test-jwt-secret-key-for-testing"
    os.environ["API_HOST"] = "127.0.0.1"
    os.environ["API_PORT"] = "8769"
    os.environ["API_RELOAD"] = "false"
    
    return Settings()


@pytest.fixture
def mock_redis_client():
    """Mock Redis client for testing."""
    mock_client = AsyncMock(spec=RedisApi)
    
    # Default mock responses
    mock_client.call.return_value = {"status": "success", "data": []}
    mock_client.get_key_data.return_value = [{"value": []}]
    mock_client.retrieve_time_series_data.return_value = {"data": []}
    
    return mock_client


@pytest.fixture
def auth_token(test_settings):
    """Generate a valid JWT token for testing."""
    payload = {
        "user_id": "test_user",
        "email": "<EMAIL>",
        "exp": 9999999999  # Far future expiration
    }
    return jwt.encode(payload, test_settings.jwt_secret, algorithm="HS256")


@pytest.fixture
def auth_headers(auth_token):
    """Authentication headers for API requests."""
    return {
        "X-API-Key": auth_token,
        "X-Tenant-Owner-Vat": "NO950700939",
        "Content-Type": "application/json"
    }


@pytest.fixture
def test_app(test_settings, mock_redis_client):
    """Create FastAPI test application with mocked dependencies."""
    with patch('app.services.redis_api.redis_api_client.RedisApi', return_value=mock_redis_client):
        app = create_api()
        return app


@pytest.fixture
def client(test_app):
    """Synchronous test client."""
    return TestClient(test_app)


@pytest.fixture
async def async_client(test_app) -> AsyncGenerator[AsyncClient, None]:
    """Asynchronous test client."""
    async with AsyncClient(app=test_app, base_url="http://test") as ac:
        yield ac


@pytest.fixture
def sample_owner_data():
    """Sample owner data for testing."""
    return {
        "owner_vat": "NO950700939",
        "owner_name": "Test Owner",
        "vessels": [
            {
                "imo": "8918461",
                "vessel_id": 32,
                "vessel_name": "Wilson Saga"
            }
        ]
    }


@pytest.fixture
def sample_vessel_data():
    """Sample vessel data for testing."""
    return {
        "imo": "8918461",
        "vessel_id": 32,
        "vessel_name": "Wilson Saga",
        "owner_vat": "NO950700939",
        "vessel_type": "General Cargo",
        "dwt": 5000,
        "built_year": 2010
    }


@pytest.fixture
def sample_time_series_data():
    """Sample time series data for testing."""
    return {
        "timestamps": ["2024-01-01T00:00:00Z", "2024-01-01T01:00:00Z"],
        "values": [100.5, 102.3],
        "metadata": {
            "vessel_imo": "8918461",
            "parameter": "fuel_consumption"
        }
    }


@pytest.fixture
def mock_managed_owners():
    """Mock managed owners data."""
    return [
        {"vat": "NO950700939", "name": "Test Owner 1"},
        {"vat": "NO123456789", "name": "Test Owner 2"}
    ]


@pytest.fixture(autouse=True)
def reset_singletons():
    """Reset singleton instances between tests."""
    # Clear RedisApi singleton instance
    if hasattr(RedisApi, '_instances'):
        RedisApi._instances.clear()
    yield
    # Clean up after test
    if hasattr(RedisApi, '_instances'):
        RedisApi._instances.clear()


@pytest.fixture
def mock_auth_dependencies(mock_managed_owners):
    """Mock authentication dependencies."""
    with patch('auth.managed_owners', return_value=mock_managed_owners), \
         patch('auth.validate_owner_access', return_value=None):
        yield


# Test data factories
class TestDataFactory:
    """Factory for creating test data."""
    
    @staticmethod
    def create_request_params(**overrides):
        """Create request parameters for testing."""
        defaults = {
            "owner_vat": "NO950700939",
            "from_date": "2024-01-01 00:00:00",
            "to_date": "2024-01-07 23:59:59",
            "vessel_imo": "8918461"
        }
        defaults.update(overrides)
        return defaults
    
    @staticmethod
    def create_redis_response(data=None, status="success"):
        """Create Redis API response for testing."""
        return {
            "status": status,
            "data": data or [],
            "message": "Success" if status == "success" else "Error"
        }


@pytest.fixture
def test_data_factory():
    """Test data factory fixture."""
    return TestDataFactory
