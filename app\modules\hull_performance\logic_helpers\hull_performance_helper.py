import json
import numpy as np
import pandas as pd

from app.modules.hull_performance.hull_performance_service import HullPerformanceService
from app.context.utils import rename_vat


class HullPerformanceHelper:

    def __init__(self, hull_performance_service: HullPerformanceService):
        self.hull_performance_service = hull_performance_service

    # Redis modification calls for search
    async def get_ai_data_in_period(self, vessel_imo, owner_vat, start_date, end_date):
        redis_data = await self.hull_performance_service.hull_ai(**rename_vat(locals(), "vessel_imo", "owner_vat"))
        dict_to_use = redis_data["all"]
        df = pd.DataFrame(dict_to_use)
        df["timestamps"] = pd.to_datetime(df["timestamps"])
        df = df[(df["timestamps"] >= start_date) & (df["timestamps"] <= end_date)]
        return df

    async def get_raw_data_in_period(self, vessel_imo, owner_vat, start_date, end_date):
        dict_to_use = {}
        redis_data = await self.hull_performance_service.hull_raw(**rename_vat(locals(), "vessel_imo", "owner_vat"))
        if redis_data is not None:
            try:
                dict_to_use = json.loads(redis_data)["search"]
            except Exception as e:
                dict_to_use = redis_data["search"]
            finally:
                df = pd.DataFrame(dict_to_use)
                df.index = pd.to_datetime(df.index)
                df = df[(df.index >= start_date) & (df.index <= end_date)]
                return df
        else:
            return None

    # Do formatting and calculations for search
    def group_data(self, df):
        grouped = df.groupby("speed_draft")
        return grouped

    def extract_speed_draft(self, group_name):
        # Extract speed and draft from the group name
        numbers_as_strings = group_name.split("_")
        numbers_as_strings = [
            num_str[:-2] if num_str.endswith(".0") else num_str
            for num_str in numbers_as_strings
        ]

        speed = float(numbers_as_strings[0])
        draft = float(numbers_as_strings[1])
        return speed, draft

    def get_min_and_latest_values(self, group_data):
        # Identify the index of the lowest point
        min_index = group_data["shaft_1_power"].idxmin()

        # Identify the index of the most current (last) point
        current_index = group_data.index[-1]

        # Extract the x and y values for these points
        min_value_shaft = group_data.loc[min_index, "shaft_1_power"]
        current_value_shaft = group_data.loc[current_index, "shaft_1_power"]

        # Extract the x and y values for these points
        min_value_fuel = group_data.loc[min_index, "new_fuel_consumption_difference"]
        current_value_fuel = group_data.loc[
            current_index, "new_fuel_consumption_difference"
        ]
        return (
            min_value_shaft,
            current_value_shaft,
            min_value_fuel,
            current_value_fuel,
            current_index,
            min_index,
        )

    def get_differences(
            self,
            current_value_shaft,
            min_value_shaft,
            current_index,
            min_index,
            current_value_fuel,
            min_value_fuel,
    ):
        # Calculate the difference in values
        value_difference = current_value_shaft - min_value_shaft

        # Calculate the time difference in days
        time_difference = (current_index - min_index).days

        # Calculate metrics
        shaft_diff = current_value_shaft - min_value_shaft
        overconsumption_fuel = current_value_fuel - min_value_fuel
        daily_rate_of_shaft_change = (
            value_difference / time_difference if time_difference != 0 else 0
        )
        shaft_diff_percentage = (
                                        (current_value_shaft - min_value_shaft) / min_value_shaft
                                ) * 100
        return (
            shaft_diff,
            overconsumption_fuel,
            daily_rate_of_shaft_change,
            shaft_diff_percentage,
        )

    def format_data(self, speed, group_name, speed_dict, dict_to_use):
        # Initialize the speed key in speed_dict if not already present
        if speed not in speed_dict:
            speed_dict[speed] = {}

        # Add the data for this group_name under the corresponding speed in speed_dict
        speed_dict[speed][group_name] = dict_to_use[group_name]

    def process_groups(self, grouped):
        # Initialize dictionaries and lists

        final_dict = {}
        speed_dict = {}
        final_dict = {"speed_trendlines": {}}

        # Main processing loop
        for group_name, group_data in grouped:
            # Vessel 39 has duplicated time stamps for some reason so we remove it
            group_data = group_data[~group_data.index.duplicated(keep="first")]

            # Resample the data to 12-hour intervals and calculate the mean
            resampled_group = (
                group_data["shaft_1_power"].resample("12h").mean().dropna()
            )

            speed, draft = self.extract_speed_draft(group_name)
            (
                min_value_shaft,
                current_value_shaft,
                min_value_fuel,
                current_value_fuel,
                current_index,
                min_index,
            ) = self.get_min_and_latest_values(group_data)
            (
                shaft_diff,
                overconsumption_fuel,
                daily_rate_of_shaft_change,
                shaft_diff_percentage,
            ) = self.get_differences(
                current_value_shaft,
                min_value_shaft,
                current_index,
                min_index,
                current_value_fuel,
                min_value_fuel,
            )

            final_dict[group_name] = {
                "speed": speed,
                "draft": draft,
                "shaft_power": round(group_data["shaft_1_power"].mean(), 2),
                "rpm": round(group_data["me_1_rpm"].mean(), 2),
                "pitch": round(group_data["prop_1_pitch"].mean(), 2),
                "shaft_diff": shaft_diff,
                "overconsumption_fuel": overconsumption_fuel,
                "daily_rate_of_shaft_change": daily_rate_of_shaft_change,
                "shaft_diff_percentage": shaft_diff_percentage,
                "best_value": min_value_shaft,
                "current_value": current_value_shaft,
                "best_value_time": min_index.strftime("%Y-%m-%d %H:%M:%S"),
                "current_value_time": current_index.strftime("%Y-%m-%d %H:%M:%S"),
                "resampled_timestamps": resampled_group.index.strftime(
                    "%Y-%m-%d %H:%M:%S"
                ).tolist(),
                "resampled_shaft_power": resampled_group.round(2).tolist(),
            }

            self.format_data(speed, group_name, speed_dict, final_dict)
        return final_dict, speed_dict

    def get_trend_line_all_data(self, resampled_data):
        # Perform rolling mean based on one week average
        x = resampled_data.index
        y = resampled_data.values

        if not isinstance(x, pd.DatetimeIndex):
            x = pd.to_datetime(x)

        data_series = pd.Series(y, index=x)
        time_window = "7D"  # 1-week time window
        moving_avg = data_series.rolling(time_window).mean()
        moving_avg = moving_avg.bfill()
        trend_line = [round(value) for value in moving_avg]
        return trend_line

    def add_all_data(self, processed_dict, resampled_data):
        # Get trend line
        trend_line = self.get_trend_line_all_data(resampled_data)

        processed_dict["all"] = {
            "all_shaft_power": resampled_data.round(2).tolist(),
            "all_timestamps": resampled_data.index.strftime(
                "%Y-%m-%d %H:%M:%S"
            ).tolist(),
            "trend_line": trend_line,
        }
        return processed_dict

    def add_speed_trendlines(self, speed_dict, added_trendline_dict):
        # Iterate over each speed and its associated data
        for speed, groups in speed_dict.items():
            combined_timestamps = []
            combined_shaft_power = []

            # Aggregate data for each speed
            for group_name, data in groups.items():
                combined_timestamps.extend(data["resampled_timestamps"])
                combined_shaft_power.extend(data["resampled_shaft_power"])

            # Sort data by timestamps
            combined_data = list(zip(combined_timestamps, combined_shaft_power))
            combined_data.sort(key=lambda x: pd.to_datetime(x[0]))  # Sort by timestamps

            # Unzip the combined_data back into separate lists
            combined_timestamps, combined_shaft_power = zip(*combined_data)

            # Convert lists to pandas Series and DatetimeIndex
            x = pd.to_datetime(combined_timestamps)
            y = pd.Series(combined_shaft_power)

            # Create Data Series with datetime index
            data_series = pd.Series(y.values, index=x)

            # Apply rolling mean with a 1-week time window
            time_window = "7D"
            moving_avg = data_series.rolling(window=time_window).mean()

            # Handle NaNs from rolling window
            moving_avg = moving_avg.bfill()

            # Round the values in the moving average to create the trend line
            trend_line = [round(value) for value in moving_avg]

            # Update the dictionary for the current speed
            added_trendline_dict["speed_trendlines"][speed] = {
                "combined_resampled_timestamps": list(combined_timestamps),
                "combined_resampled_shaft_power": list(combined_shaft_power),
                "trend_line": trend_line,
            }
        return added_trendline_dict

    def get_gages_values(self, performance_data):
        # Parse timestamps and sort the DataFrame
        performance_data["timestamps"] = pd.to_datetime(performance_data["timestamps"])
        performance_data = performance_data.sort_values("timestamps")

        # Determine the number of weeks (assuming one row per week)
        num_weeks = performance_data.shape[0]

        # Aggregate values across all weeks
        total_real = performance_data["real_shaft_powers"].sum()
        total_predicted = performance_data["predicted_shaft_powers"].sum()
        total_fuel = performance_data["fuel_consumptions"].sum()

        # Compute overall relative change as a percentage
        overall_relative_changes = (
                                           (total_real - total_predicted) / np.abs(total_predicted)
                                   ) * 100

        # Compute overall adjusted fuel consumption using the aggregated fuel and overall relative change
        overall_adjusted_fuel = total_fuel * (1 + overall_relative_changes / 100)

        # Compute overall fuel consumption difference
        overall_fuel_diff = overall_adjusted_fuel - total_fuel

        # Compute overall shaft power difference (raw)
        overall_shaft_diff = total_real - total_predicted

        # Now convert the overall differences to per-week averages where applicable
        avg_week_fuel_diff = overall_fuel_diff / num_weeks
        avg_week_shaft_diff = overall_shaft_diff / num_weeks
        # The overall_relative_changes already represents the overall percentage difference

        performance_data["shaft_diff"] = (
                performance_data["real_shaft_powers"]
                - performance_data["predicted_shaft_powers"]
        )

        avg_daily_rate = (performance_data["shaft_diff"].diff() / 7).mean()

        return (
            avg_week_fuel_diff,
            avg_daily_rate,
            overall_relative_changes,
            avg_week_shaft_diff,
        )

    def calculate_gages_value(self, performance_data):
        (
            average_overconsumption_fuel,
            average_daily_rate_of_shaft_change,
            average_shaft_diff_percentage,
            average_shaft_diff,
        ) = self.get_gages_values(performance_data)

        performance_data = {
            "average_shaft_power_changes_percentage": average_shaft_diff_percentage,
            "average_shaft_power_changes": average_shaft_diff,
            "average_fuel_changes": average_overconsumption_fuel,
            "average_daily_rate_of_change": average_daily_rate_of_shaft_change,
        }
        return performance_data
