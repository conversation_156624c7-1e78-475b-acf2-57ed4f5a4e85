# Vessel Router - API endpoints for vessel management operations

from fastapi import APIRouter, Depends
from auth import owner_vat_check

from app.modules.vessel.vessel_logic import VesselLogic
from app.modules.vessel.vessel_service import VesselService
from app.context.context import get_context

# Create router with vessels tag
router = APIRouter(tags=["Vessels"])

# Set up dependency injection for logic and service layers
get_vessel_params, get_vessel_logic = get_context(
    VesselLogic,
    VesselService,
)


@router.get("/vessels", dependencies=[Depends(owner_vat_check)])
async def vessels(
        params: dict = Depends(get_vessel_params),
        logic: VesselLogic = Depends(get_vessel_logic),
):
    # GET endpoint to retrieve all vessels for an owner
    return await logic.vessels(**params)
