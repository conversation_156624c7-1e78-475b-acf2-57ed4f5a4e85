import logging
import logging.config
import platform

ERROR_FORMAT = "%(levelname)s at %(asctime)s in %(funcName)s in %(filename) at line %(lineno)d: %(message)s"
DEBUG_FORMAT = "%(lineno)d in %(filename)s at %(asctime)s: %(message)s"

if platform.system() == "Windows":
    logfile = "c:/etc/frugaltech.log"
else:
    logfile = "/var/log/frugaltech.log"

LOG_CONFIG = {
    "version": 1,
    "formatters": {
        "error": {"format": ERROR_FORMAT},
        "debug": {"format": DEBUG_FORMAT},
    },
    "handlers": {
        "console": {"class": "logging.StreamHandler", "level": logging.WARNING},
        "file": {
            "class": "logging.FileHandler",
            "filename": logfile,
            "formatter": "error",
            "level": logging.DEBUG,
        },
    },
    "root": {"handlers": ("console", "file"), "level": "INFO"},
}
logging.config.dictConfig(LOG_CONFIG)
logging.getLogger().setLevel(logging.INFO)
from configparser import RawConfigParser
import sys
import os

__author__ = "ls"


class ConfigurationHelper:
    def __init__(self):
        if len(sys.argv) > 1 and os.path.isfile(sys.argv[1]):
            configfile = sys.argv[1]
        else:
            if platform.system() == "Windows":
                configfile = "C:/etc/frugaltech.config"
            else:
                configfile = "/etc/frugaltech.config"

        try:
            self.configuration = RawConfigParser()
            self.configuration.read(configfile)

            if self.configuration is None or len(self.configuration.sections()) == 0:
                raise NameError(
                    "Wrong or empty config file {} defined".format(configfile)
                )

        except NameError as e:
            logging.error("Error while reading config file: %s", e)
            raise

    def get_section_as_dict(self, section="plc-tags"):
        config_dict = None
        try:
            config_dict = dict(self.configuration.items(section))
            for key, value in config_dict.items():
                if str.isdecimal(value):
                    config_dict[key] = int(value)
        except Exception as e:
            logging.error(
                "Unable to retrieve section %s from config. Error was %s", section, e
            )
        return config_dict

    def get_list_of_regs(self, register_names=None, config_section="plc-tags"):
        register_dict = self.get_section_as_dict(config_section)
        # If we want more registers than are actually available, it's a problem.
        if register_names is None or len(register_names) > len(register_dict.keys()):
            raise (ValueError("List of registers is shorter than the list of indexes"))
        list_of_registers = []
        for name in register_names:
            register = register_dict.get(name)
            if register is None:
                message = "No value available for register named {}".format(name)
                raise NameError(message)
            list_of_registers.append(register)
        return list_of_registers

    def instance(self):
        log_level = self.configuration.getint("general", "log-level")
        logging.getLogger().setLevel(log_level)
        return self.configuration
