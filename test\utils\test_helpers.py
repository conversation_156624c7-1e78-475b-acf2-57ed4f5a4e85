"""
Test utilities and helper functions.
"""
import asyncio
import time
from typing import Any, Dict, List, Optional, Callable
from unittest.mock import AsyncMock, MagicMock
from contextlib import asynccontextmanager
import pytest


class MockRedisResponse:
    """Mock Redis response for testing."""
    
    def __init__(self, data: Any = None, status: str = "success", error: str = None):
        self.data = data or []
        self.status = status
        self.error = error
    
    def to_dict(self):
        result = {"status": self.status, "data": self.data}
        if self.error:
            result["error"] = self.error
        return result


class AsyncTestHelper:
    """Helper class for async testing."""
    
    @staticmethod
    async def run_concurrent_tasks(tasks: List[Callable], timeout: float = 30.0):
        """Run multiple async tasks concurrently and return results."""
        async_tasks = [task() if asyncio.iscoroutinefunction(task) else task for task in tasks]
        return await asyncio.wait_for(asyncio.gather(*async_tasks), timeout=timeout)
    
    @staticmethod
    async def measure_execution_time(coro):
        """Measure execution time of an async coroutine."""
        start_time = time.time()
        result = await coro
        end_time = time.time()
        return result, end_time - start_time
    
    @staticmethod
    @asynccontextmanager
    async def timeout_context(timeout: float):
        """Context manager for timeout testing."""
        try:
            yield await asyncio.wait_for(asyncio.sleep(0), timeout=timeout)
        except asyncio.TimeoutError:
            pytest.fail(f"Operation timed out after {timeout} seconds")


class MockFactory:
    """Factory for creating mock objects."""
    
    @staticmethod
    def create_mock_redis_client(responses: Dict[str, Any] = None):
        """Create a mock Redis client with predefined responses."""
        mock_client = AsyncMock()
        
        if responses:
            for method, response in responses.items():
                getattr(mock_client, method).return_value = response
        
        # Default responses
        mock_client.call.return_value = MockRedisResponse().to_dict()
        mock_client.get_key_data.return_value = [{"value": []}]
        
        return mock_client
    
    @staticmethod
    def create_mock_auth_handler(valid_token: bool = True):
        """Create a mock authentication handler."""
        mock_handler = MagicMock()
        
        if valid_token:
            mock_handler.return_value = None  # No exception
        else:
            from fastapi import HTTPException
            mock_handler.side_effect = HTTPException(status_code=401, detail="Invalid token")
        
        return mock_handler


class APITestHelper:
    """Helper class for API testing."""
    
    @staticmethod
    def assert_response_structure(response_data: Dict, expected_keys: List[str]):
        """Assert that response has expected structure."""
        for key in expected_keys:
            assert key in response_data, f"Expected key '{key}' not found in response"
    
    @staticmethod
    def assert_error_response(response_data: Dict, expected_status_code: int = None):
        """Assert that response is an error response."""
        assert "error" in response_data or "detail" in response_data
        if expected_status_code:
            assert response_data.get("status_code") == expected_status_code
    
    @staticmethod
    def create_test_payload(**kwargs):
        """Create a test payload with default values."""
        defaults = {
            "owner_vat": "NO950700939",
            "from_date": "2024-01-01 00:00:00",
            "to_date": "2024-01-07 23:59:59"
        }
        defaults.update(kwargs)
        return defaults


class PerformanceTestHelper:
    """Helper class for performance testing."""
    
    @staticmethod
    async def measure_concurrent_requests(client, endpoint: str, num_requests: int = 10, **kwargs):
        """Measure performance of concurrent requests."""
        start_time = time.time()
        
        tasks = []
        for _ in range(num_requests):
            if kwargs.get('method', 'GET').upper() == 'POST':
                task = client.post(endpoint, json=kwargs.get('json', {}), headers=kwargs.get('headers', {}))
            else:
                task = client.get(endpoint, headers=kwargs.get('headers', {}))
            tasks.append(task)
        
        responses = await asyncio.gather(*tasks, return_exceptions=True)
        end_time = time.time()
        
        # Analyze results
        successful_responses = [r for r in responses if not isinstance(r, Exception) and r.status_code < 400]
        failed_responses = [r for r in responses if isinstance(r, Exception) or r.status_code >= 400]
        
        return {
            "total_time": end_time - start_time,
            "num_requests": num_requests,
            "successful": len(successful_responses),
            "failed": len(failed_responses),
            "success_rate": len(successful_responses) / num_requests,
            "avg_response_time": (end_time - start_time) / num_requests,
            "responses": responses
        }
    
    @staticmethod
    def assert_performance_thresholds(results: Dict, max_response_time: float = 5.0, min_success_rate: float = 0.9):
        """Assert performance meets thresholds."""
        assert results["avg_response_time"] <= max_response_time, \
            f"Average response time {results['avg_response_time']:.2f}s exceeds threshold {max_response_time}s"
        
        assert results["success_rate"] >= min_success_rate, \
            f"Success rate {results['success_rate']:.2%} below threshold {min_success_rate:.2%}"


class DatabaseTestHelper:
    """Helper class for database testing."""
    
    @staticmethod
    def create_test_data_set(size: int = 10):
        """Create a test dataset."""
        return [
            {
                "id": i,
                "name": f"Test Item {i}",
                "value": i * 10,
                "timestamp": f"2024-01-{i:02d}T00:00:00Z"
            }
            for i in range(1, size + 1)
        ]
    
    @staticmethod
    def assert_data_integrity(original_data: List[Dict], retrieved_data: List[Dict]):
        """Assert data integrity between original and retrieved data."""
        assert len(original_data) == len(retrieved_data), "Data count mismatch"
        
        for orig, retr in zip(original_data, retrieved_data):
            for key in orig.keys():
                assert key in retr, f"Missing key '{key}' in retrieved data"
                assert orig[key] == retr[key], f"Value mismatch for key '{key}'"


# Pytest fixtures for test helpers
@pytest.fixture
def async_test_helper():
    """Async test helper fixture."""
    return AsyncTestHelper()


@pytest.fixture
def mock_factory():
    """Mock factory fixture."""
    return MockFactory()


@pytest.fixture
def api_test_helper():
    """API test helper fixture."""
    return APITestHelper()


@pytest.fixture
def performance_test_helper():
    """Performance test helper fixture."""
    return PerformanceTestHelper()


@pytest.fixture
def database_test_helper():
    """Database test helper fixture."""
    return DatabaseTestHelper()
