# Vessel Service - handles data access for vessel management operations

from app.services.redis_api.redis_api_client import RedisApi


class VesselService:
    # Service layer for vessel data operations

    def __init__(self, redis_client: RedisApi = None):
        # Initialize with Redis client (create new one if not provided)
        self.redis = redis_client or RedisApi()

    async def vessels(self, **params):
        # Get vessels data from Redis API
        return await self.redis.call(
            path="vessel",
            key="vessels",
            **params
        )
